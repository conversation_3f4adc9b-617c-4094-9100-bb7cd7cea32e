import React from 'react';

interface RadioOption {
  value: string;
  label: string;
  disabled?: boolean;
  description?: string;
}

interface RadioFieldProps {
  label?: string;
  options: RadioOption[];
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  error?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  name?: string;
  inline?: boolean;
  size?: 'sm' | 'lg';
}

export default function RadioField({ 
  label,
  options,
  value,
  onChange,
  onBlur,
  error,
  helperText,
  required = false,
  disabled = false,
  className = '',
  name,
  inline = false,
  size
}: RadioFieldProps) {
  const radioName = name || `radio-${Math.random().toString(36).substr(2, 9)}`;
  const hasError = !!error;
  
  const sizeClass = size ? `form-check-input-${size}` : '';
  const errorClass = hasError ? 'is-invalid' : '';
  
  const radioClasses = [
    'form-check-input',
    sizeClass,
    errorClass
  ].filter(Boolean).join(' ');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e.target.value);
    }
  };

  return (
    <div className="mb-3">
      {label && (
        <label className="form-label">
          {label}
          {required && <span className="text-danger ms-1">*</span>}
        </label>
      )}
      
      <div className={className}>
        {options.map((option) => (
          <div 
            key={option.value} 
            className={`form-check ${inline ? 'form-check-inline' : ''}`}
          >
            <input
              id={`${radioName}-${option.value}`}
              name={radioName}
              type="radio"
              className={radioClasses}
              value={option.value}
              checked={value === option.value}
              onChange={handleChange}
              onBlur={onBlur}
              required={required}
              disabled={disabled || option.disabled}
            />
            <label 
              htmlFor={`${radioName}-${option.value}`} 
              className="form-check-label"
            >
              <div>
                {option.label}
                {option.description && (
                  <div className="form-text mt-0">
                    {option.description}
                  </div>
                )}
              </div>
            </label>
          </div>
        ))}
      </div>
      
      {error && (
        <div className="invalid-feedback d-block">
          {error}
        </div>
      )}
      
      {helperText && !error && (
        <div className="form-text">
          {helperText}
        </div>
      )}
    </div>
  );
}

// Single Radio Component
interface SingleRadioProps {
  label?: string;
  checked?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  id?: string;
  name?: string;
  value?: string;
  size?: 'sm' | 'lg';
}

export function SingleRadio({ 
  label,
  checked = false,
  onChange,
  onBlur,
  error,
  required = false,
  disabled = false,
  className = '',
  id,
  name,
  value,
  size
}: SingleRadioProps) {
  const radioId = id || `radio-${Math.random().toString(36).substr(2, 9)}`;
  const hasError = !!error;
  
  const sizeClass = size ? `form-check-input-${size}` : '';
  const errorClass = hasError ? 'is-invalid' : '';
  
  const radioClasses = [
    'form-check-input',
    sizeClass,
    errorClass
  ].filter(Boolean).join(' ');

  const containerClasses = [
    'form-check',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className="mb-3">
      <div className={containerClasses}>
        <input
          id={radioId}
          name={name}
          type="radio"
          className={radioClasses}
          checked={checked}
          onChange={onChange}
          onBlur={onBlur}
          required={required}
          disabled={disabled}
          value={value}
        />
        {label && (
          <label htmlFor={radioId} className="form-check-label">
            {label}
            {required && <span className="text-danger ms-1">*</span>}
          </label>
        )}
      </div>
      
      {error && (
        <div className="invalid-feedback d-block">
          {error}
        </div>
      )}
    </div>
  );
}
