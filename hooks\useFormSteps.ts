import { useState } from 'react';

export function useFormSteps(totalSteps: number) {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({});

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const previousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    if (step >= 1 && step <= totalSteps) {
      setCurrentStep(step);
    }
  };

  const updateFormData = (stepData: any) => {
    setFormData(prev => ({ ...prev, ...stepData }));
  };

  const resetForm = () => {
    setCurrentStep(1);
    setFormData({});
  };

  return {
    currentStep,
    formData,
    nextStep,
    previousStep,
    goToStep,
    updateFormData,
    resetForm,
    isFirstStep: currentStep === 1,
    isLastStep: currentStep === totalSteps,
  };
}
