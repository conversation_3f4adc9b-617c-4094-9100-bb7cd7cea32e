interface StatusBadgeProps {
  status: 'pending' | 'approved' | 'rejected' | 'in-review';
}

export default function StatusBadge({ status }: StatusBadgeProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'green';
      case 'rejected': return 'red';
      case 'pending': return 'yellow';
      case 'in-review': return 'blue';
      default: return 'gray';
    }
  };

  return (
    <span style={{ color: getStatusColor(status) }}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
}
