import React, { useState } from 'react';
import Link from 'next/link';

interface NavItem {
  label: string;
  href: string;
  active?: boolean;
  disabled?: boolean;
}

interface NavbarProps {
  brand?: {
    text?: string;
    logo?: string;
    href?: string;
  };
  items?: NavItem[];
  variant?: 'light' | 'dark';
  expand?: 'sm' | 'md' | 'lg' | 'xl' | 'xxl' | false;
  fixed?: 'top' | 'bottom';
  sticky?: 'top';
  className?: string;
  children?: React.ReactNode;
  actions?: React.ReactNode;
}

export default function Navbar({
  brand = { text: 'KYC Portal', href: '/' },
  items = [],
  variant = 'light',
  expand = 'lg',
  fixed,
  sticky,
  className = '',
  children,
  actions
}: NavbarProps) {
  const [isCollapsed, setIsCollapsed] = useState(true);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const navbarClasses = [
    'navbar',
    variant === 'dark' ? 'navbar-dark bg-dark' : 'navbar-light bg-light',
    expand ? `navbar-expand-${expand}` : '',
    fixed ? `fixed-${fixed}` : '',
    sticky ? `sticky-${sticky}` : '',
    className
  ].filter(Boolean).join(' ');

  const collapseClasses = [
    'collapse navbar-collapse',
    !isCollapsed ? 'show' : ''
  ].filter(Boolean).join(' ');

  return (
    <nav className={navbarClasses}>
      <div className="container-fluid">
        {/* Brand */}
        {brand && (
          <Link href={brand.href || '/'} className="navbar-brand">
            {brand.logo && (
              <img
                src={brand.logo}
                alt={brand.text || 'Logo'}
                height="30"
                className="d-inline-block align-text-top me-2"
              />
            )}
            {brand.text}
          </Link>
        )}

        {/* Toggler for mobile */}
        {expand && (
          <button
            className="navbar-toggler"
            type="button"
            onClick={toggleCollapse}
            aria-controls="navbarNav"
            aria-expanded={!isCollapsed}
            aria-label="Toggle navigation"
          >
            <span className="navbar-toggler-icon"></span>
          </button>
        )}

        {/* Collapsible content */}
        <div className={collapseClasses} id="navbarNav">
          {/* Navigation items */}
          {items.length > 0 && (
            <ul className="navbar-nav me-auto mb-2 mb-lg-0">
              {items.map((item, index) => (
                <li key={index} className="nav-item">
                  <Link
                    href={item.href}
                    className={`nav-link ${item.active ? 'active' : ''} ${item.disabled ? 'disabled' : ''}`}
                    aria-current={item.active ? 'page' : undefined}
                  >
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          )}

          {/* Custom children */}
          {children}

          {/* Actions (typically buttons, user menu, etc.) */}
          {actions && (
            <div className="d-flex">
              {actions}
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}

// Navbar Brand Component
interface NavbarBrandProps {
  children: React.ReactNode;
  href?: string;
  className?: string;
}

export function NavbarBrand({ children, href = '/', className = '' }: NavbarBrandProps) {
  return (
    <Link href={href} className={`navbar-brand ${className}`}>
      {children}
    </Link>
  );
}

// Navbar Nav Component
interface NavbarNavProps {
  children: React.ReactNode;
  className?: string;
}

export function NavbarNav({ children, className = '' }: NavbarNavProps) {
  return (
    <ul className={`navbar-nav ${className}`}>
      {children}
    </ul>
  );
}

// Nav Item Component
interface NavItemProps {
  children: React.ReactNode;
  href: string;
  active?: boolean;
  disabled?: boolean;
  className?: string;
}

export function NavItem({ children, href, active = false, disabled = false, className = '' }: NavItemProps) {
  const linkClasses = [
    'nav-link',
    active ? 'active' : '',
    disabled ? 'disabled' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <li className="nav-item">
      <Link
        href={href}
        className={linkClasses}
        aria-current={active ? 'page' : undefined}
      >
        {children}
      </Link>
    </li>
  );
}
