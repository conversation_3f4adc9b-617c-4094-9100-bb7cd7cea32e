interface ToastMessageProps {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  isVisible: boolean;
  onClose: () => void;
}

export default function ToastMessage({ message, type, isVisible, onClose }: ToastMessageProps) {
  if (!isVisible) return null;

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success': return 'green';
      case 'error': return 'red';
      case 'warning': return 'orange';
      case 'info': return 'blue';
      default: return 'gray';
    }
  };

  return (
    <div style={{ backgroundColor: getTypeColor(type) }}>
      <span>{message}</span>
      <button onClick={onClose}>×</button>
    </div>
  );
}
