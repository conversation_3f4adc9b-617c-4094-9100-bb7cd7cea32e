import { create } from 'zustand';

interface KycState {
  applications: any[];
  currentApplication: any | null;
  setApplications: (applications: any[]) => void;
  setCurrentApplication: (application: any) => void;
  updateApplication: (id: string, updates: any) => void;
  addApplication: (application: any) => void;
}

export const useKycStore = create<KycState>((set) => ({
  applications: [],
  currentApplication: null,
  setApplications: (applications) => set({ applications }),
  setCurrentApplication: (application) => set({ currentApplication: application }),
  updateApplication: (id, updates) => set((state) => ({
    applications: state.applications.map(app => 
      app.id === id ? { ...app, ...updates } : app
    ),
    currentApplication: state.currentApplication?.id === id 
      ? { ...state.currentApplication, ...updates } 
      : state.currentApplication
  })),
  addApplication: (application) => set((state) => ({
    applications: [...state.applications, application]
  })),
}));
