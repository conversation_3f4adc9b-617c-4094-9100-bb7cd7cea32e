import React from 'react';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  pill?: boolean;
  className?: string;
  size?: 'sm' | 'lg';
}

export default function Badge({ 
  children, 
  variant = 'primary', 
  pill = false,
  className = '',
  size
}: BadgeProps) {
  const baseClasses = 'badge';
  const variantClass = `bg-${variant}`;
  const pillClass = pill ? 'rounded-pill' : '';
  const sizeClass = size ? `badge-${size}` : '';
  
  const badgeClasses = [
    baseClasses,
    variantClass,
    pillClass,
    sizeClass,
    className
  ].filter(Boolean).join(' ');

  return (
    <span className={badgeClasses}>
      {children}
    </span>
  );
}

// Status Badge Component for KYC statuses
interface StatusBadgeProps {
  status: 'pending' | 'approved' | 'rejected' | 'in-review' | 'incomplete' | 'expired';
  className?: string;
}

export function StatusBadge({ status, className = '' }: StatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'approved':
        return { variant: 'success', text: 'Approved' };
      case 'rejected':
        return { variant: 'danger', text: 'Rejected' };
      case 'pending':
        return { variant: 'warning', text: 'Pending' };
      case 'in-review':
        return { variant: 'info', text: 'In Review' };
      case 'incomplete':
        return { variant: 'secondary', text: 'Incomplete' };
      case 'expired':
        return { variant: 'dark', text: 'Expired' };
      default:
        return { variant: 'secondary', text: 'Unknown' };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Badge variant={config.variant as any} pill className={className}>
      {config.text}
    </Badge>
  );
}
