import React from 'react';

interface ProgressProps {
  value: number;
  max?: number;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  striped?: boolean;
  animated?: boolean;
  size?: 'sm' | 'lg';
  label?: string;
  showValue?: boolean;
  className?: string;
}

export default function Progress({
  value,
  max = 100,
  variant = 'primary',
  striped = false,
  animated = false,
  size,
  label,
  showValue = false,
  className = ''
}: ProgressProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

  const progressClasses = [
    'progress',
    size === 'sm' ? 'progress-sm' : '',
    size === 'lg' ? 'progress-lg' : '',
    className
  ].filter(Boolean).join(' ');

  const barClasses = [
    'progress-bar',
    `bg-${variant}`,
    striped ? 'progress-bar-striped' : '',
    animated ? 'progress-bar-animated' : ''
  ].filter(Boolean).join(' ');

  const progressStyle = size === 'sm' ? { height: '0.5rem' } : size === 'lg' ? { height: '1.5rem' } : {};

  return (
    <div className="progress-container">
      {label && (
        <div className="d-flex justify-content-between align-items-center mb-1">
          <span className="small">{label}</span>
          {showValue && (
            <span className="small text-muted">
              {Math.round(percentage)}%
            </span>
          )}
        </div>
      )}
      <div className={progressClasses} style={progressStyle}>
        <div
          className={barClasses}
          role="progressbar"
          style={{ width: `${percentage}%` }}
          aria-valuenow={value}
          aria-valuemin={0}
          aria-valuemax={max}
        >
          {showValue && !label && (
            <span>{Math.round(percentage)}%</span>
          )}
        </div>
      </div>
    </div>
  );
}

// Multi Progress Component
interface ProgressSegment {
  value: number;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  label?: string;
}

interface MultiProgressProps {
  segments: ProgressSegment[];
  max?: number;
  striped?: boolean;
  animated?: boolean;
  size?: 'sm' | 'lg';
  className?: string;
  showLabels?: boolean;
}

export function MultiProgress({
  segments,
  max = 100,
  striped = false,
  animated = false,
  size,
  className = '',
  showLabels = false
}: MultiProgressProps) {
  const totalValue = segments.reduce((sum, segment) => sum + segment.value, 0);

  const progressClasses = [
    'progress',
    size === 'sm' ? 'progress-sm' : '',
    size === 'lg' ? 'progress-lg' : '',
    className
  ].filter(Boolean).join(' ');

  const progressStyle = size === 'sm' ? { height: '0.5rem' } : size === 'lg' ? { height: '1.5rem' } : {};

  return (
    <div className="progress-container">
      {showLabels && (
        <div className="d-flex justify-content-between align-items-center mb-1">
          <div className="d-flex gap-3">
            {segments.map((segment, index) => (
              <div key={index} className="d-flex align-items-center">
                <div
                  className={`bg-${segment.variant || 'primary'} me-1`}
                  style={{ width: '12px', height: '12px', borderRadius: '2px' }}
                ></div>
                <span className="small">{segment.label}</span>
              </div>
            ))}
          </div>
          <span className="small text-muted">
            {Math.round((totalValue / max) * 100)}%
          </span>
        </div>
      )}
      <div className={progressClasses} style={progressStyle}>
        {segments.map((segment, index) => {
          const percentage = Math.min(Math.max((segment.value / max) * 100, 0), 100);
          const barClasses = [
            'progress-bar',
            `bg-${segment.variant || 'primary'}`,
            striped ? 'progress-bar-striped' : '',
            animated ? 'progress-bar-animated' : ''
          ].filter(Boolean).join(' ');

          return (
            <div
              key={index}
              className={barClasses}
              role="progressbar"
              style={{ width: `${percentage}%` }}
              aria-valuenow={segment.value}
              aria-valuemin={0}
              aria-valuemax={max}
              title={segment.label}
            />
          );
        })}
      </div>
    </div>
  );
}

// Circular Progress Component
interface CircularProgressProps {
  value: number;
  max?: number;
  size?: number;
  strokeWidth?: number;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  showValue?: boolean;
  label?: string;
  className?: string;
}

export function CircularProgress({
  value,
  max = 100,
  size = 120,
  strokeWidth = 8,
  variant = 'primary',
  showValue = true,
  label,
  className = ''
}: CircularProgressProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  const getVariantColor = (variant: string) => {
    const colors = {
      primary: '#0d6efd',
      secondary: '#6c757d',
      success: '#198754',
      danger: '#dc3545',
      warning: '#ffc107',
      info: '#0dcaf0',
      light: '#f8f9fa',
      dark: '#212529'
    };
    return colors[variant as keyof typeof colors] || colors.primary;
  };

  return (
    <div className={`d-flex flex-column align-items-center ${className}`}>
      <div className="position-relative">
        <svg width={size} height={size} className="transform-rotate-90">
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#e9ecef"
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={getVariantColor(variant)}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            style={{
              transition: 'stroke-dashoffset 0.3s ease-in-out'
            }}
          />
        </svg>
        {showValue && (
          <div
            className="position-absolute top-50 start-50 translate-middle text-center"
            style={{ fontSize: size * 0.12 }}
          >
            <div className="fw-bold">{Math.round(percentage)}%</div>
          </div>
        )}
      </div>
      {label && (
        <div className="mt-2 text-center small text-muted">
          {label}
        </div>
      )}
    </div>
  );
}
