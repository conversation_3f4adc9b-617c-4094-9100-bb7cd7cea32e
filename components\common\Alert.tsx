import React, { useState } from 'react';

interface AlertProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  dismissible?: boolean;
  onClose?: () => void;
  className?: string;
  icon?: React.ReactNode;
  title?: string;
}

export default function Alert({ 
  children, 
  variant = 'info', 
  dismissible = false,
  onClose,
  className = '',
  icon,
  title
}: AlertProps) {
  const [isVisible, setIsVisible] = useState(true);

  const handleClose = () => {
    setIsVisible(false);
    onClose?.();
  };

  if (!isVisible) return null;

  const baseClasses = 'alert';
  const variantClass = `alert-${variant}`;
  const dismissibleClass = dismissible ? 'alert-dismissible' : '';
  
  const alertClasses = [
    baseClasses,
    variantClass,
    dismissibleClass,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={alertClasses} role="alert">
      <div className="d-flex align-items-start">
        {icon && (
          <div className="me-3 mt-1">
            {icon}
          </div>
        )}
        <div className="flex-grow-1">
          {title && (
            <h6 className="alert-heading mb-2">{title}</h6>
          )}
          {children}
        </div>
        {dismissible && (
          <button
            type="button"
            className="btn-close"
            aria-label="Close"
            onClick={handleClose}
          ></button>
        )}
      </div>
    </div>
  );
}

// Predefined Alert variants for common use cases
export function SuccessAlert({ children, ...props }: Omit<AlertProps, 'variant'>) {
  return (
    <Alert variant="success" {...props}>
      {children}
    </Alert>
  );
}

export function ErrorAlert({ children, ...props }: Omit<AlertProps, 'variant'>) {
  return (
    <Alert variant="danger" {...props}>
      {children}
    </Alert>
  );
}

export function WarningAlert({ children, ...props }: Omit<AlertProps, 'variant'>) {
  return (
    <Alert variant="warning" {...props}>
      {children}
    </Alert>
  );
}

export function InfoAlert({ children, ...props }: Omit<AlertProps, 'variant'>) {
  return (
    <Alert variant="info" {...props}>
      {children}
    </Alert>
  );
}
