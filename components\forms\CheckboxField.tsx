import React from 'react';

interface CheckboxFieldProps {
  label?: string;
  checked?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  error?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  id?: string;
  name?: string;
  value?: string;
  indeterminate?: boolean;
  size?: 'sm' | 'lg';
}

export default function CheckboxField({ 
  label,
  checked = false,
  onChange,
  onBlur,
  error,
  helperText,
  required = false,
  disabled = false,
  className = '',
  id,
  name,
  value,
  indeterminate = false,
  size
}: CheckboxFieldProps) {
  const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;
  const hasError = !!error;
  
  const sizeClass = size ? `form-check-input-${size}` : '';
  const errorClass = hasError ? 'is-invalid' : '';
  
  const checkboxClasses = [
    'form-check-input',
    sizeClass,
    errorClass
  ].filter(Boolean).join(' ');

  const containerClasses = [
    'form-check',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className="mb-3">
      <div className={containerClasses}>
        <input
          id={checkboxId}
          name={name}
          type="checkbox"
          className={checkboxClasses}
          checked={checked}
          onChange={onChange}
          onBlur={onBlur}
          required={required}
          disabled={disabled}
          value={value}
          ref={(input) => {
            if (input) input.indeterminate = indeterminate;
          }}
          aria-describedby={error ? `${checkboxId}-error` : helperText ? `${checkboxId}-help` : undefined}
          aria-invalid={hasError}
        />
        {label && (
          <label htmlFor={checkboxId} className="form-check-label">
            {label}
            {required && <span className="text-danger ms-1">*</span>}
          </label>
        )}
      </div>
      
      {error && (
        <div id={`${checkboxId}-error`} className="invalid-feedback d-block">
          {error}
        </div>
      )}
      
      {helperText && !error && (
        <div id={`${checkboxId}-help`} className="form-text">
          {helperText}
        </div>
      )}
    </div>
  );
}

// Checkbox Group Component
interface CheckboxOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface CheckboxGroupProps {
  label?: string;
  options: CheckboxOption[];
  value?: string[];
  onChange?: (selectedValues: string[]) => void;
  error?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  inline?: boolean;
}

export function CheckboxGroup({
  label,
  options,
  value = [],
  onChange,
  error,
  helperText,
  required = false,
  disabled = false,
  className = '',
  inline = false
}: CheckboxGroupProps) {
  const groupId = `checkbox-group-${Math.random().toString(36).substr(2, 9)}`;
  const hasError = !!error;

  const handleCheckboxChange = (optionValue: string, checked: boolean) => {
    if (!onChange) return;
    
    if (checked) {
      onChange([...value, optionValue]);
    } else {
      onChange(value.filter(v => v !== optionValue));
    }
  };

  return (
    <div className="mb-3">
      {label && (
        <label className="form-label">
          {label}
          {required && <span className="text-danger ms-1">*</span>}
        </label>
      )}
      
      <div className={className}>
        {options.map((option) => (
          <div 
            key={option.value} 
            className={`form-check ${inline ? 'form-check-inline' : ''}`}
          >
            <input
              id={`${groupId}-${option.value}`}
              type="checkbox"
              className={`form-check-input ${hasError ? 'is-invalid' : ''}`}
              checked={value.includes(option.value)}
              onChange={(e) => handleCheckboxChange(option.value, e.target.checked)}
              disabled={disabled || option.disabled}
              value={option.value}
            />
            <label 
              htmlFor={`${groupId}-${option.value}`} 
              className="form-check-label"
            >
              {option.label}
            </label>
          </div>
        ))}
      </div>
      
      {error && (
        <div className="invalid-feedback d-block">
          {error}
        </div>
      )}
      
      {helperText && !error && (
        <div className="form-text">
          {helperText}
        </div>
      )}
    </div>
  );
}
