interface KycStepFormProps {
  step: number;
  onNext: () => void;
  onPrevious: () => void;
}

export default function KycStepForm({ step, onNext, onPrevious }: KycStepFormProps) {
  return (
    <div>
      <h2>KYC Step {step}</h2>
      <form>
        {/* Form content based on step */}
        <div>
          <button type="button" onClick={onPrevious}>Previous</button>
          <button type="button" onClick={onNext}>Next</button>
        </div>
      </form>
    </div>
  );
}
