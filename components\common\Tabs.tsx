import React, { useState } from 'react';

interface TabItem {
  id: string;
  label: string;
  content: React.ReactNode;
  disabled?: boolean;
  badge?: {
    text: string;
    variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';
  };
}

interface TabsProps {
  items: TabItem[];
  defaultActiveTab?: string;
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
  variant?: 'tabs' | 'pills';
  justified?: boolean;
  vertical?: boolean;
  className?: string;
  contentClassName?: string;
}

export default function Tabs({
  items,
  defaultActiveTab,
  activeTab,
  onTabChange,
  variant = 'tabs',
  justified = false,
  vertical = false,
  className = '',
  contentClassName = ''
}: TabsProps) {
  const [internalActiveTab, setInternalActiveTab] = useState(
    activeTab || defaultActiveTab || items[0]?.id
  );

  const currentActiveTab = activeTab || internalActiveTab;

  const handleTabClick = (tabId: string) => {
    if (items.find(item => item.id === tabId)?.disabled) return;
    
    setInternalActiveTab(tabId);
    onTabChange?.(tabId);
  };

  const navClasses = [
    'nav',
    variant === 'pills' ? 'nav-pills' : 'nav-tabs',
    justified ? 'nav-justified' : '',
    vertical ? 'flex-column' : '',
    className
  ].filter(Boolean).join(' ');

  const activeContent = items.find(item => item.id === currentActiveTab)?.content;

  const tabsContainer = vertical ? 'd-flex' : '';
  const navContainer = vertical ? 'me-3' : 'mb-3';
  const contentContainer = vertical ? 'flex-grow-1' : '';

  return (
    <div className={tabsContainer}>
      {/* Tab Navigation */}
      <ul className={`${navClasses} ${navContainer}`} role="tablist">
        {items.map((item) => (
          <li key={item.id} className="nav-item" role="presentation">
            <button
              className={`nav-link ${currentActiveTab === item.id ? 'active' : ''} ${item.disabled ? 'disabled' : ''}`}
              type="button"
              role="tab"
              aria-controls={`${item.id}-pane`}
              aria-selected={currentActiveTab === item.id}
              disabled={item.disabled}
              onClick={() => handleTabClick(item.id)}
            >
              <span>{item.label}</span>
              {item.badge && (
                <span className={`badge bg-${item.badge.variant || 'primary'} ms-2`}>
                  {item.badge.text}
                </span>
              )}
            </button>
          </li>
        ))}
      </ul>

      {/* Tab Content */}
      <div className={`tab-content ${contentContainer} ${contentClassName}`}>
        <div
          className="tab-pane fade show active"
          id={`${currentActiveTab}-pane`}
          role="tabpanel"
          aria-labelledby={`${currentActiveTab}-tab`}
        >
          {activeContent}
        </div>
      </div>
    </div>
  );
}

// Controlled Tabs Component
interface ControlledTabsProps {
  children: React.ReactElement<TabPaneProps>[];
  activeKey?: string;
  defaultActiveKey?: string;
  onSelect?: (key: string) => void;
  variant?: 'tabs' | 'pills';
  justified?: boolean;
  vertical?: boolean;
  className?: string;
}

export function ControlledTabs({
  children,
  activeKey,
  defaultActiveKey,
  onSelect,
  variant = 'tabs',
  justified = false,
  vertical = false,
  className = ''
}: ControlledTabsProps) {
  const [internalActiveKey, setInternalActiveKey] = useState(
    activeKey || defaultActiveKey || children[0]?.props.eventKey
  );

  const currentActiveKey = activeKey || internalActiveKey;

  const handleSelect = (key: string) => {
    setInternalActiveKey(key);
    onSelect?.(key);
  };

  const navClasses = [
    'nav',
    variant === 'pills' ? 'nav-pills' : 'nav-tabs',
    justified ? 'nav-justified' : '',
    vertical ? 'flex-column' : '',
    className
  ].filter(Boolean).join(' ');

  const tabsContainer = vertical ? 'd-flex' : '';
  const navContainer = vertical ? 'me-3' : 'mb-3';
  const contentContainer = vertical ? 'flex-grow-1' : '';

  return (
    <div className={tabsContainer}>
      {/* Tab Navigation */}
      <ul className={`${navClasses} ${navContainer}`} role="tablist">
        {children.map((child) => (
          <li key={child.props.eventKey} className="nav-item" role="presentation">
            <button
              className={`nav-link ${currentActiveKey === child.props.eventKey ? 'active' : ''} ${child.props.disabled ? 'disabled' : ''}`}
              type="button"
              role="tab"
              disabled={child.props.disabled}
              onClick={() => handleSelect(child.props.eventKey)}
            >
              {child.props.title}
            </button>
          </li>
        ))}
      </ul>

      {/* Tab Content */}
      <div className={`tab-content ${contentContainer}`}>
        {children.map((child) => (
          <div
            key={child.props.eventKey}
            className={`tab-pane fade ${currentActiveKey === child.props.eventKey ? 'show active' : ''}`}
            role="tabpanel"
          >
            {currentActiveKey === child.props.eventKey && child.props.children}
          </div>
        ))}
      </div>
    </div>
  );
}

// Tab Pane Component
interface TabPaneProps {
  eventKey: string;
  title: string;
  children: React.ReactNode;
  disabled?: boolean;
}

export function TabPane({ children }: TabPaneProps) {
  return <>{children}</>;
}
