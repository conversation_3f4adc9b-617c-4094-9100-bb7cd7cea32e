import React, { useState, useRef } from 'react';

interface FileUploadProps {
  label?: string;
  accept?: string;
  multiple?: boolean;
  onChange?: (files: FileList | null) => void;
  onDrop?: (files: FileList) => void;
  error?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  id?: string;
  name?: string;
  maxSize?: number; // in bytes
  maxFiles?: number;
  preview?: boolean;
  dragAndDrop?: boolean;
}

export default function FileUpload({
  label,
  accept,
  multiple = false,
  onChange,
  onDrop,
  error,
  helperText,
  required = false,
  disabled = false,
  className = '',
  id,
  name,
  maxSize,
  maxFiles,
  preview = false,
  dragAndDrop = true
}: FileUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const fileId = id || `file-${Math.random().toString(36).substring(2, 11)}`;
  const hasError = !!error;

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    handleFiles(files);
  };

  const handleFiles = (files: FileList | null) => {
    if (!files) return;

    const fileArray = Array.from(files);

    // Validate file size
    if (maxSize) {
      const oversizedFiles = fileArray.filter(file => file.size > maxSize);
      if (oversizedFiles.length > 0) {
        // Handle error - could set error state or call error callback
        return;
      }
    }

    // Validate file count
    if (maxFiles && fileArray.length > maxFiles) {
      // Handle error - could set error state or call error callback
      return;
    }

    setSelectedFiles(fileArray);
    onChange?.(files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled && dragAndDrop) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (disabled || !dragAndDrop) return;

    const files = e.dataTransfer.files;
    handleFiles(files);
    onDrop?.(files);
  };

  const handleClick = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  const removeFile = (index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);

    // Create new FileList-like object
    const dt = new DataTransfer();
    newFiles.forEach(file => dt.items.add(file));
    onChange?.(dt.files);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const dropzoneClasses = [
    'border border-2 border-dashed rounded p-4 text-center cursor-pointer transition-colors',
    isDragOver ? 'border-primary bg-light' : 'border-secondary',
    disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary',
    hasError ? 'border-danger' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className="mb-3">
      {label && (
        <label className="form-label">
          {label}
          {required && <span className="text-danger ms-1">*</span>}
        </label>
      )}

      {dragAndDrop ? (
        <div
          className={dropzoneClasses}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          <div className="d-flex flex-column align-items-center">
            <svg className="mb-2" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="7,10 12,15 17,10" />
              <line x1="12" y1="15" x2="12" y2="3" />
            </svg>
            <p className="mb-2">
              <strong>Click to upload</strong> or drag and drop
            </p>
            {accept && (
              <p className="text-muted small mb-0">
                Accepted formats: {accept}
              </p>
            )}
            {maxSize && (
              <p className="text-muted small mb-0">
                Max file size: {formatFileSize(maxSize)}
              </p>
            )}
          </div>
        </div>
      ) : (
        <input
          ref={fileInputRef}
          id={fileId}
          name={name}
          type="file"
          className={`form-control ${hasError ? 'is-invalid' : ''}`}
          accept={accept}
          multiple={multiple}
          onChange={handleFileChange}
          required={required}
          disabled={disabled}
        />
      )}

      <input
        ref={fileInputRef}
        type="file"
        className="d-none"
        accept={accept}
        multiple={multiple}
        onChange={handleFileChange}
        required={required}
        disabled={disabled}
      />

      {/* File Preview */}
      {preview && selectedFiles.length > 0 && (
        <div className="mt-3">
          <h6>Selected Files:</h6>
          <div className="list-group">
            {selectedFiles.map((file, index) => (
              <div key={index} className="list-group-item d-flex justify-content-between align-items-center">
                <div>
                  <strong>{file.name}</strong>
                  <small className="text-muted ms-2">({formatFileSize(file.size)})</small>
                </div>
                <button
                  type="button"
                  className="btn btn-sm btn-outline-danger"
                  onClick={() => removeFile(index)}
                  disabled={disabled}
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {error && (
        <div className="invalid-feedback d-block">
          {error}
        </div>
      )}

      {helperText && !error && (
        <div className="form-text">
          {helperText}
        </div>
      )}
    </div>
  );
}
