import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  border?: boolean;
  hover?: boolean;
}

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
  actions?: React.ReactNode;
}

interface CardBodyProps {
  children: React.ReactNode;
  className?: string;
}

interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

interface CardTitleProps {
  children: React.ReactNode;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

interface CardTextProps {
  children: React.ReactNode;
  className?: string;
  muted?: boolean;
}

// Main Card Component
export default function Card({ 
  children, 
  className = '', 
  shadow = 'md',
  border = true,
  hover = false
}: CardProps) {
  const shadowClass = shadow !== 'none' ? `shadow-${shadow}` : '';
  const borderClass = border ? 'border' : 'border-0';
  const hoverClass = hover ? 'card-hover' : '';
  
  const cardClasses = [
    'card',
    shadowClass,
    borderClass,
    hoverClass,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={cardClasses}>
      {children}
    </div>
  );
}

// Card Header Component
export function CardHeader({ children, className = '', actions }: CardHeaderProps) {
  return (
    <div className={`card-header d-flex justify-content-between align-items-center ${className}`}>
      <div>{children}</div>
      {actions && <div>{actions}</div>}
    </div>
  );
}

// Card Body Component
export function CardBody({ children, className = '' }: CardBodyProps) {
  return (
    <div className={`card-body ${className}`}>
      {children}
    </div>
  );
}

// Card Footer Component
export function CardFooter({ children, className = '' }: CardFooterProps) {
  return (
    <div className={`card-footer ${className}`}>
      {children}
    </div>
  );
}

// Card Title Component
export function CardTitle({ children, className = '', as: Component = 'h5' }: CardTitleProps) {
  return (
    <Component className={`card-title ${className}`}>
      {children}
    </Component>
  );
}

// Card Text Component
export function CardText({ children, className = '', muted = false }: CardTextProps) {
  const mutedClass = muted ? 'text-muted' : '';
  
  return (
    <p className={`card-text ${mutedClass} ${className}`}>
      {children}
    </p>
  );
}
