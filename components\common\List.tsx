import React from 'react';
import Link from 'next/link';

interface ListItem {
  id: string | number;
  content: React.ReactNode;
  href?: string;
  active?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  badge?: {
    text: string;
    variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  };
  actions?: React.ReactNode;
}

interface ListProps {
  items: ListItem[];
  flush?: boolean;
  numbered?: boolean;
  horizontal?: boolean | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  className?: string;
  onItemClick?: (item: ListItem, index: number) => void;
}

export default function List({
  items,
  flush = false,
  numbered = false,
  horizontal = false,
  className = '',
  onItemClick
}: ListProps) {
  const listClasses = [
    numbered ? 'list-group list-group-numbered' : 'list-group',
    flush ? 'list-group-flush' : '',
    horizontal ? (horizontal === true ? 'list-group-horizontal' : `list-group-horizontal-${horizontal}`) : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={listClasses}>
      {items.map((item, index) => {
        const itemClasses = [
          'list-group-item',
          item.active ? 'active' : '',
          item.disabled ? 'disabled' : '',
          item.variant ? `list-group-item-${item.variant}` : '',
          item.href || onItemClick ? 'list-group-item-action' : ''
        ].filter(Boolean).join(' ');

        const handleClick = () => {
          if (!item.disabled && onItemClick) {
            onItemClick(item, index);
          }
        };

        const content = (
          <div className="d-flex justify-content-between align-items-center w-100">
            <div className="flex-grow-1">
              {item.content}
            </div>
            <div className="d-flex align-items-center gap-2">
              {item.badge && (
                <span className={`badge bg-${item.badge.variant || 'primary'}`}>
                  {item.badge.text}
                </span>
              )}
              {item.actions && (
                <div onClick={(e) => e.stopPropagation()}>
                  {item.actions}
                </div>
              )}
            </div>
          </div>
        );

        if (item.href) {
          return (
            <Link
              key={item.id}
              href={item.href}
              className={itemClasses}
              onClick={handleClick}
            >
              {content}
            </Link>
          );
        }

        return (
          <div
            key={item.id}
            className={itemClasses}
            onClick={handleClick}
            style={{ cursor: onItemClick && !item.disabled ? 'pointer' : 'default' }}
          >
            {content}
          </div>
        );
      })}
    </div>
  );
}

// Simple List Component
interface SimpleListProps {
  children: React.ReactNode;
  flush?: boolean;
  numbered?: boolean;
  horizontal?: boolean | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  className?: string;
}

export function SimpleList({
  children,
  flush = false,
  numbered = false,
  horizontal = false,
  className = ''
}: SimpleListProps) {
  const listClasses = [
    numbered ? 'list-group list-group-numbered' : 'list-group',
    flush ? 'list-group-flush' : '',
    horizontal ? (horizontal === true ? 'list-group-horizontal' : `list-group-horizontal-${horizontal}`) : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={listClasses}>
      {children}
    </div>
  );
}

// List Item Component
interface ListItemComponentProps {
  children: React.ReactNode;
  href?: string;
  active?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  className?: string;
  onClick?: () => void;
}

export function ListItem({
  children,
  href,
  active = false,
  disabled = false,
  variant,
  className = '',
  onClick
}: ListItemComponentProps) {
  const itemClasses = [
    'list-group-item',
    active ? 'active' : '',
    disabled ? 'disabled' : '',
    variant ? `list-group-item-${variant}` : '',
    href || onClick ? 'list-group-item-action' : '',
    className
  ].filter(Boolean).join(' ');

  if (href) {
    return (
      <Link href={href} className={itemClasses} onClick={onClick}>
        {children}
      </Link>
    );
  }

  return (
    <div
      className={itemClasses}
      onClick={onClick}
      style={{ cursor: onClick && !disabled ? 'pointer' : 'default' }}
    >
      {children}
    </div>
  );
}

// Description List Component
interface DescriptionItem {
  term: React.ReactNode;
  description: React.ReactNode;
}

interface DescriptionListProps {
  items: DescriptionItem[];
  horizontal?: boolean;
  className?: string;
}

export function DescriptionList({
  items,
  horizontal = false,
  className = ''
}: DescriptionListProps) {
  const dlClasses = [
    horizontal ? 'row' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <dl className={dlClasses}>
      {items.map((item, index) => (
        <React.Fragment key={index}>
          <dt className={horizontal ? 'col-sm-3' : ''}>
            {item.term}
          </dt>
          <dd className={horizontal ? 'col-sm-9' : ''}>
            {item.description}
          </dd>
        </React.Fragment>
      ))}
    </dl>
  );
}
