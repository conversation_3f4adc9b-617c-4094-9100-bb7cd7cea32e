/* Dashboard Styles */

.dashboard-container {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background-color: var(--dark-color);
  color: white;
  padding: 1rem;
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav li {
  margin-bottom: 0.5rem;
}

.sidebar-nav a {
  color: white;
  text-decoration: none;
  padding: 0.75rem 1rem;
  display: block;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.sidebar-nav a:hover,
.sidebar-nav a.active {
  background-color: rgba(255, 255, 255, 0.1);
}

.main-content {
  flex: 1;
  padding: 2rem;
  background-color: #f8f9fa;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.dashboard-subtitle {
  color: var(--secondary-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--secondary-color);
  font-size: 0.875rem;
}

.dashboard-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.dashboard-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #dee2e6;
}

.dashboard-card-body {
  padding: 1.5rem;
}
