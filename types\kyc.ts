export interface KycApplication {
  id: string;
  userId: string;
  status: KycApplicationStatus;
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  rejectionReason?: string;
  personalInfo: PersonalInfo;
  address: Address;
  documents: KycDocument[];
  riskScore?: number;
  notes?: string[];
  createdAt: string;
  updatedAt?: string;
}

export type KycApplicationStatus = 'draft' | 'pending' | 'in_review' | 'approved' | 'rejected' | 'expired';

export interface PersonalInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  nationality: string;
  placeOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  occupation: string;
  employerName?: string;
  annualIncome?: number;
  sourceOfFunds: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  addressType?: 'residential' | 'business';
}

export interface KycDocument {
  id: string;
  applicationId: string;
  type: DocumentType;
  name: string;
  filename: string;
  size: number;
  mimeType: string;
  url: string;
  status: DocumentStatus;
  uploadedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  rejectionReason?: string;
  expiryDate?: string;
  documentNumber?: string;
  issuingCountry?: string;
}

export type DocumentType = 
  | 'passport'
  | 'drivers_license'
  | 'national_id'
  | 'utility_bill'
  | 'bank_statement'
  | 'proof_of_income'
  | 'proof_of_address'
  | 'selfie'
  | 'other';

export type DocumentStatus = 'pending' | 'approved' | 'rejected' | 'expired';

export interface KycFormData {
  step: number;
  personalInfo: Partial<PersonalInfo>;
  address: Partial<Address>;
  documents: File[];
  isComplete: boolean;
}

export interface KycReview {
  applicationId: string;
  reviewerId: string;
  status: KycApplicationStatus;
  notes: string;
  rejectionReason?: string;
  documentReviews: DocumentReview[];
}

export interface DocumentReview {
  documentId: string;
  status: DocumentStatus;
  notes?: string;
  rejectionReason?: string;
}
