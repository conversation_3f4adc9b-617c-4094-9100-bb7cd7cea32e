/* Bootstrap Overrides for KYC Portal */

:root {
  /* KYC Portal Brand Colors */
  --bs-primary: #2563eb;
  --bs-primary-rgb: 37, 99, 235;
  --bs-secondary: #64748b;
  --bs-secondary-rgb: 100, 116, 139;
  --bs-success: #059669;
  --bs-success-rgb: 5, 150, 105;
  --bs-danger: #dc2626;
  --bs-danger-rgb: 220, 38, 38;
  --bs-warning: #d97706;
  --bs-warning-rgb: 217, 119, 6;
  --bs-info: #0891b2;
  --bs-info-rgb: 8, 145, 178;
  --bs-light: #f8fafc;
  --bs-light-rgb: 248, 250, 252;
  --bs-dark: #1e293b;
  --bs-dark-rgb: 30, 41, 59;

  /* Custom KYC Colors */
  --kyc-accent: #7c3aed;
  --kyc-accent-rgb: 124, 58, 237;
  --kyc-neutral: #f1f5f9;
  --kyc-neutral-rgb: 241, 245, 249;

  /* Border radius */
  --bs-border-radius: 0.5rem;
  --bs-border-radius-sm: 0.375rem;
  --bs-border-radius-lg: 0.75rem;
  --bs-border-radius-xl: 1rem;

  /* Shadows */
  --bs-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --bs-box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --bs-box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Button Enhancements */
.btn {
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  border-radius: var(--bs-border-radius);
}

.btn-primary {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  box-shadow: var(--bs-box-shadow-sm);
}

.btn-primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: var(--bs-box-shadow);
}

.btn-outline-primary {
  color: var(--bs-primary);
  border-color: var(--bs-primary);
}

.btn-outline-primary:hover {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
}

/* Card Enhancements */
.card {
  border-radius: var(--bs-border-radius-lg);
  box-shadow: var(--bs-box-shadow);
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease-in-out;
}

.card:hover {
  box-shadow: var(--bs-box-shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  background-color: var(--kyc-neutral);
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
  border-radius: var(--bs-border-radius-lg) var(--bs-border-radius-lg) 0 0 !important;
}

.card-body {
  padding: 1.5rem;
}

/* Form Enhancements */
.form-control {
  border-radius: var(--bs-border-radius);
  border: 1px solid #d1d5db;
  transition: all 0.2s ease-in-out;
  font-size: 0.875rem;
}

.form-control:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.form-label {
  font-weight: 500;
  color: var(--bs-dark);
  margin-bottom: 0.5rem;
}

.form-select {
  border-radius: var(--bs-border-radius);
  border: 1px solid #d1d5db;
}

.form-select:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* Badge Enhancements */
.badge {
  font-weight: 500;
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

.badge.rounded-pill {
  padding: 0.375rem 0.875rem;
}

/* Alert Enhancements */
.alert {
  border-radius: var(--bs-border-radius);
  border: none;
  box-shadow: var(--bs-box-shadow-sm);
}

.alert-dismissible .btn-close {
  padding: 0.75rem 1rem;
}

/* Modal Enhancements */
.modal-content {
  border-radius: var(--bs-border-radius-lg);
  border: none;
  box-shadow: var(--bs-box-shadow-lg);
}

.modal-header {
  border-bottom: 1px solid #e2e8f0;
  padding: 1.5rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid #e2e8f0;
  padding: 1.5rem;
}

/* Table Enhancements */
.table {
  border-radius: var(--bs-border-radius);
  overflow: hidden;
}

.table thead th {
  border-bottom: 2px solid #e2e8f0;
  font-weight: 600;
  background-color: var(--kyc-neutral);
  padding: 1rem 0.75rem;
}

.table tbody td {
  padding: 0.875rem 0.75rem;
  border-bottom: 1px solid #f1f5f9;
}

.table-hover tbody tr:hover {
  background-color: rgba(37, 99, 235, 0.05);
}

/* Navigation Enhancements */
.navbar {
  box-shadow: var(--bs-box-shadow-sm);
}

.navbar-brand {
  font-weight: 600;
  font-size: 1.25rem;
}

.nav-tabs {
  border-bottom: 2px solid #e2e8f0;
}

.nav-tabs .nav-link {
  border: none;
  border-bottom: 2px solid transparent;
  padding: 0.75rem 1rem;
  font-weight: 500;
  color: var(--bs-secondary);
}

.nav-tabs .nav-link:hover {
  border-bottom-color: var(--bs-primary);
  color: var(--bs-primary);
}

.nav-tabs .nav-link.active {
  border-bottom-color: var(--bs-primary);
  color: var(--bs-primary);
  background-color: transparent;
}

.nav-pills .nav-link {
  border-radius: var(--bs-border-radius);
  font-weight: 500;
  padding: 0.5rem 1rem;
}

.nav-pills .nav-link.active {
  background-color: var(--bs-primary);
}

/* List Group Enhancements */
.list-group {
  border-radius: var(--bs-border-radius);
}

.list-group-item {
  border: 1px solid #e2e8f0;
  padding: 0.875rem 1rem;
}

.list-group-item:first-child {
  border-top-left-radius: var(--bs-border-radius);
  border-top-right-radius: var(--bs-border-radius);
}

.list-group-item:last-child {
  border-bottom-left-radius: var(--bs-border-radius);
  border-bottom-right-radius: var(--bs-border-radius);
}

.list-group-item-action:hover {
  background-color: var(--kyc-neutral);
}

/* Progress Enhancements */
.progress {
  border-radius: var(--bs-border-radius);
  background-color: #e2e8f0;
}

.progress-bar {
  border-radius: var(--bs-border-radius);
}

.progress-sm {
  height: 0.5rem;
}

.progress-lg {
  height: 1.5rem;
}

/* Toast Enhancements */
.toast {
  border-radius: var(--bs-border-radius);
  box-shadow: var(--bs-box-shadow-lg);
  border: 1px solid #e2e8f0;
}

.toast-header {
  border-bottom: 1px solid #e2e8f0;
  padding: 0.75rem 1rem;
}

.toast-body {
  padding: 0.75rem 1rem;
}

/* Breadcrumb Enhancements */
.breadcrumb {
  background-color: transparent;
  padding: 0.75rem 0;
  margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: var(--bs-secondary);
}

/* Spinner Enhancements */
.spinner-border,
.spinner-grow {
  width: 2rem;
  height: 2rem;
}

.spinner-border-sm,
.spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}

/* Utility Classes */
.cursor-pointer {
  cursor: pointer;
}

.hover-bg-light:hover {
  background-color: var(--kyc-neutral) !important;
}

.hover-bg-secondary:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.hover-text-primary:hover {
  color: var(--bs-primary) !important;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: var(--bs-box-shadow-lg);
}

.transform-rotate-90 {
  transform: rotate(-90deg);
}

/* Custom KYC Specific Styles */
.kyc-status-pending {
  color: var(--bs-warning);
  background-color: rgba(217, 119, 6, 0.1);
}

.kyc-status-approved {
  color: var(--bs-success);
  background-color: rgba(5, 150, 105, 0.1);
}

.kyc-status-rejected {
  color: var(--bs-danger);
  background-color: rgba(220, 38, 38, 0.1);
}

.kyc-status-in-review {
  color: var(--bs-info);
  background-color: rgba(8, 145, 178, 0.1);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Table Overrides */
.table {
  border-radius: 8px;
  overflow: hidden;
}

.table thead th {
  background-color: var(--light-color);
  border-bottom: 2px solid #dee2e6;
}
