# KYC Portal Components Library

A comprehensive collection of reusable React components built with Bootstrap 5.3.3 for the KYC Portal application. All components follow the single responsibility principle and are designed to be modular, accessible, and easy to use.

## 🚀 Features

- **Bootstrap 5.3.3** - Latest Bootstrap with custom theming
- **TypeScript** - Full type safety and IntelliSense support
- **Responsive Design** - Mobile-first approach
- **Accessibility** - ARIA compliant components
- **Customizable** - Extensive props for customization
- **Consistent** - Unified design system across all components

## 📦 Installation

The components are already set up in your project. Bootstrap is installed and configured with custom overrides.

```bash
npm install bootstrap@latest  # Already installed
```

## 🎨 Theming

Custom theme variables are defined in `styles/bootstrap-overrides.css`:

```css
:root {
  --bs-primary: #2563eb;
  --bs-success: #059669;
  --bs-danger: #dc2626;
  --kyc-accent: #7c3aed;
  --kyc-neutral: #f1f5f9;
}
```

## 📚 Component Categories

### Common Components
- **Button** - Versatile button with multiple variants and states
- **Card** - Flexible content container with header, body, and footer
- **Badge** - Status indicators and labels
- **Alert** - Contextual feedback messages
- **Modal** - Dialog overlays for important content
- **Loader/Spinner** - Loading states and progress indicators
- **Toast** - Non-intrusive notifications
- **Tooltip** - Contextual help and information
- **Tabs** - Content organization and navigation
- **Breadcrumb** - Navigation hierarchy
- **List** - Organized content display
- **EmptyState** - Placeholder for empty content areas

### Form Components
- **InputField** - Text input with validation and icons
- **SelectField** - Dropdown selection with search
- **TextareaField** - Multi-line text input
- **CheckboxField** - Single and group checkboxes
- **RadioField** - Single and group radio buttons
- **FileUpload** - Drag-and-drop file upload with preview

### Layout Components
- **Container** - Responsive content wrapper
- **Row/Col** - Bootstrap grid system
- **Navbar** - Application navigation header
- **Sidebar** - Collapsible side navigation
- **Footer** - Application footer with links

### Data Display Components
- **DataTable** - Feature-rich data table with sorting, selection
- **Progress** - Progress bars and circular progress
- **Pagination** - Table and list pagination

## 🔧 Usage Examples

### Basic Button
```tsx
import { Button } from '@/components';

<Button variant="primary" size="lg" onClick={handleClick}>
  Click Me
</Button>
```

### Form with Validation
```tsx
import { InputField, SelectField, Button } from '@/components';

<form>
  <InputField
    label="Email"
    type="email"
    value={email}
    onChange={(e) => setEmail(e.target.value)}
    error={emailError}
    required
  />
  
  <SelectField
    label="Country"
    options={countryOptions}
    value={country}
    onChange={(e) => setCountry(e.target.value)}
    placeholder="Select a country"
  />
  
  <Button type="submit" loading={isSubmitting}>
    Submit
  </Button>
</form>
```

### Data Table
```tsx
import { DataTable, Badge } from '@/components';

const columns = [
  { key: 'name', label: 'Name', sortable: true },
  { key: 'status', label: 'Status', render: (value) => 
    <Badge variant={value === 'active' ? 'success' : 'secondary'}>
      {value}
    </Badge>
  }
];

<DataTable
  columns={columns}
  data={users}
  striped
  hover
  selectable
  onSelectionChange={setSelectedUsers}
/>
```

### Modal Dialog
```tsx
import { Modal, ModalHeader, ModalBody, ModalFooter, Button } from '@/components';

<Modal isOpen={showModal} onClose={() => setShowModal(false)}>
  <ModalHeader>Confirm Action</ModalHeader>
  <ModalBody>
    Are you sure you want to proceed?
  </ModalBody>
  <ModalFooter>
    <Button variant="secondary" onClick={() => setShowModal(false)}>
      Cancel
    </Button>
    <Button variant="danger" onClick={handleConfirm}>
      Confirm
    </Button>
  </ModalFooter>
</Modal>
```

## 🎯 Component Showcase

Visit `/components-showcase` to see all components in action with interactive examples.

## 🔍 TypeScript Support

All components are fully typed with TypeScript interfaces:

```tsx
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'success' | 'danger';
  size?: 'sm' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  // ... more props
}
```

## 🎨 Customization

### Custom Variants
Components support Bootstrap's color variants plus custom KYC-specific variants:
- `primary`, `secondary`, `success`, `danger`, `warning`, `info`, `light`, `dark`

### Size Options
Most components support size variants:
- `sm` (small), default, `lg` (large)

### Custom Classes
All components accept a `className` prop for additional styling:

```tsx
<Button className="my-custom-class" variant="primary">
  Custom Styled Button
</Button>
```

## 📱 Responsive Design

All components are built with mobile-first responsive design:
- Grid system with breakpoints: `xs`, `sm`, `md`, `lg`, `xl`, `xxl`
- Responsive utilities for hiding/showing content
- Touch-friendly interactive elements

## ♿ Accessibility

Components follow WCAG guidelines:
- Proper ARIA attributes
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- Color contrast compliance

## 🧪 Testing

Components are designed to be easily testable:
- Semantic HTML structure
- Predictable class names
- Proper ARIA labels and roles
- Event handlers for user interactions

## 📖 Best Practices

1. **Keep components under 100 lines** - Split large components into smaller ones
2. **Use TypeScript interfaces** - Define clear prop types
3. **Follow naming conventions** - Use descriptive, consistent names
4. **Handle loading states** - Show appropriate feedback during async operations
5. **Validate user input** - Provide clear error messages
6. **Test accessibility** - Use screen readers and keyboard navigation

## 🔄 Updates

The component library is continuously updated with:
- New components based on application needs
- Performance improvements
- Accessibility enhancements
- Bug fixes and refinements

For questions or contributions, please refer to the project documentation.
