/* Admin Panel Styles */

.admin-container {
  display: flex;
  min-height: 100vh;
}

.admin-sidebar {
  width: 280px;
  background-color: #2c3e50;
  color: white;
  padding: 1rem;
}

.admin-logo {
  text-align: center;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 1rem;
}

.admin-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.admin-nav li {
  margin-bottom: 0.25rem;
}

.admin-nav a {
  color: white;
  text-decoration: none;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.admin-nav a:hover,
.admin-nav a.active {
  background-color: #34495e;
  transform: translateX(4px);
}

.admin-nav-icon {
  margin-right: 0.75rem;
  width: 1.25rem;
  height: 1.25rem;
}

.admin-main {
  flex: 1;
  background-color: #ecf0f1;
}

.admin-header {
  background: white;
  padding: 1rem 2rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-content {
  padding: 2rem;
}

.admin-page-title {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #2c3e50;
}

.admin-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.data-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  padding: 1.5rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-search {
  max-width: 300px;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-approved {
  background-color: #d4edda;
  color: #155724;
}

.status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.status-in-review {
  background-color: #cce7ff;
  color: #004085;
}
