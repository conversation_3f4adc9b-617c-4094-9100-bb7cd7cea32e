import React from 'react';
import Link from 'next/link';

interface SidebarItem {
  label: string;
  href: string;
  icon?: React.ReactNode;
  active?: boolean;
  disabled?: boolean;
  badge?: {
    text: string;
    variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';
  };
  children?: SidebarItem[];
}

interface SidebarProps {
  items: SidebarItem[];
  className?: string;
  variant?: 'light' | 'dark';
  width?: string;
  collapsible?: boolean;
  collapsed?: boolean;
  onToggle?: () => void;
  header?: React.ReactNode;
  footer?: React.ReactNode;
}

export default function Sidebar({
  items,
  className = '',
  variant = 'light',
  width = '250px',
  collapsible = false,
  collapsed = false,
  onToggle,
  header,
  footer
}: SidebarProps) {
  const sidebarClasses = [
    'd-flex flex-column',
    variant === 'dark' ? 'bg-dark text-white' : 'bg-light border-end',
    className
  ].filter(Boolean).join(' ');

  const sidebarStyle = {
    width: collapsed ? '60px' : width,
    minHeight: '100vh',
    transition: 'width 0.3s ease'
  };

  return (
    <aside className={sidebarClasses} style={sidebarStyle}>
      {/* Header */}
      {header && (
        <div className="p-3 border-bottom">
          {header}
        </div>
      )}

      {/* Toggle button for collapsible sidebar */}
      {collapsible && (
        <div className="p-2 border-bottom">
          <button
            className={`btn btn-sm w-100 ${variant === 'dark' ? 'btn-outline-light' : 'btn-outline-dark'}`}
            onClick={onToggle}
            aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {collapsed ? '→' : '←'}
          </button>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-grow-1 p-2">
        <SidebarNav items={items} collapsed={collapsed} variant={variant} />
      </nav>

      {/* Footer */}
      {footer && (
        <div className="p-3 border-top mt-auto">
          {footer}
        </div>
      )}
    </aside>
  );
}

// Sidebar Navigation Component
interface SidebarNavProps {
  items: SidebarItem[];
  collapsed?: boolean;
  variant?: 'light' | 'dark';
  level?: number;
}

function SidebarNav({ items, collapsed = false, variant = 'light', level = 0 }: SidebarNavProps) {
  return (
    <ul className="list-unstyled">
      {items.map((item, index) => (
        <SidebarNavItem
          key={index}
          item={item}
          collapsed={collapsed}
          variant={variant}
          level={level}
        />
      ))}
    </ul>
  );
}

// Sidebar Navigation Item Component
interface SidebarNavItemProps {
  item: SidebarItem;
  collapsed?: boolean;
  variant?: 'light' | 'dark';
  level?: number;
}

function SidebarNavItem({ item, collapsed = false, variant = 'light', level = 0 }: SidebarNavItemProps) {
  const [isExpanded, setIsExpanded] = React.useState(false);
  const hasChildren = item.children && item.children.length > 0;

  const linkClasses = [
    'd-flex align-items-center text-decoration-none p-2 rounded mb-1',
    item.active ? (variant === 'dark' ? 'bg-primary' : 'bg-primary text-white') : '',
    item.disabled ? 'opacity-50' : (variant === 'dark' ? 'text-white hover-bg-secondary' : 'text-dark hover-bg-light'),
    level > 0 ? 'ms-3' : ''
  ].filter(Boolean).join(' ');

  const handleClick = (e: React.MouseEvent) => {
    if (hasChildren) {
      e.preventDefault();
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <li>
      <Link
        href={item.href}
        className={linkClasses}
        onClick={handleClick}
        title={collapsed ? item.label : undefined}
      >
        {/* Icon */}
        {item.icon && (
          <span className={`${collapsed ? '' : 'me-2'}`}>
            {item.icon}
          </span>
        )}

        {/* Label */}
        {!collapsed && (
          <>
            <span className="flex-grow-1">{item.label}</span>

            {/* Badge */}
            {item.badge && (
              <span className={`badge bg-${item.badge.variant || 'primary'} ms-2`}>
                {item.badge.text}
              </span>
            )}

            {/* Expand/Collapse indicator */}
            {hasChildren && (
              <span className="ms-2">
                {isExpanded ? '▼' : '▶'}
              </span>
            )}
          </>
        )}
      </Link>

      {/* Children */}
      {hasChildren && !collapsed && isExpanded && (
        <SidebarNav
          items={item.children!}
          collapsed={collapsed}
          variant={variant}
          level={level + 1}
        />
      )}
    </li>
  );
}
