import React, { useState, useMemo } from 'react';

interface TableColumn<T = any> {
  key: string;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: T, index: number) => React.ReactNode;
  width?: string;
  align?: 'left' | 'center' | 'right';
  className?: string;
}

interface DataTableProps<T = any> {
  columns: TableColumn<T>[];
  data: T[];
  loading?: boolean;
  striped?: boolean;
  bordered?: boolean;
  hover?: boolean;
  responsive?: boolean | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  size?: 'sm' | 'lg';
  variant?: 'dark' | 'light';
  className?: string;
  emptyMessage?: string;
  onRowClick?: (row: T, index: number) => void;
  selectable?: boolean;
  selectedRows?: T[];
  onSelectionChange?: (selectedRows: T[]) => void;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
}

export default function DataTable<T = any>({
  columns,
  data,
  loading = false,
  striped = false,
  bordered = false,
  hover = true,
  responsive = true,
  size,
  variant,
  className = '',
  emptyMessage = 'No data available',
  onRowClick,
  selectable = false,
  selectedRows = [],
  onSelectionChange,
  sortBy,
  sortDirection = 'asc',
  onSort
}: DataTableProps<T>) {
  const [internalSortBy, setInternalSortBy] = useState<string | undefined>(sortBy);
  const [internalSortDirection, setInternalSortDirection] = useState<'asc' | 'desc'>(sortDirection);

  // Handle sorting
  const handleSort = (column: TableColumn<T>) => {
    if (!column.sortable) return;

    let newDirection: 'asc' | 'desc' = 'asc';

    if (internalSortBy === column.key) {
      newDirection = internalSortDirection === 'asc' ? 'desc' : 'asc';
    }

    setInternalSortBy(column.key);
    setInternalSortDirection(newDirection);

    if (onSort) {
      onSort(column.key, newDirection);
    }
  };

  // Sort data if no external sorting is provided
  const sortedData = useMemo(() => {
    if (onSort || !internalSortBy) return data;

    return [...data].sort((a, b) => {
      const aValue = a[internalSortBy];
      const bValue = b[internalSortBy];

      if (aValue === bValue) return 0;

      const comparison = aValue < bValue ? -1 : 1;
      return internalSortDirection === 'asc' ? comparison : -comparison;
    });
  }, [data, internalSortBy, internalSortDirection, onSort]);

  // Handle row selection
  const handleSelectAll = (checked: boolean) => {
    if (!onSelectionChange) return;
    onSelectionChange(checked ? [...sortedData] : []);
  };

  const handleSelectRow = (row: T, checked: boolean) => {
    if (!onSelectionChange) return;

    if (checked) {
      onSelectionChange([...selectedRows, row]);
    } else {
      onSelectionChange(selectedRows.filter(r => r !== row));
    }
  };

  const isAllSelected = selectedRows.length === sortedData.length && sortedData.length > 0;
  const isIndeterminate = selectedRows.length > 0 && selectedRows.length < sortedData.length;

  // Table classes
  const tableClasses = [
    'table',
    striped ? 'table-striped' : '',
    bordered ? 'table-bordered' : '',
    hover ? 'table-hover' : '',
    size ? `table-${size}` : '',
    variant ? `table-${variant}` : '',
    className
  ].filter(Boolean).join(' ');

  const tableElement = (
    <table className={tableClasses}>
      <thead>
        <tr>
          {selectable && (
            <th style={{ width: '50px' }}>
              <input
                type="checkbox"
                className="form-check-input"
                checked={isAllSelected}
                ref={(input) => {
                  if (input) input.indeterminate = isIndeterminate;
                }}
                onChange={(e) => handleSelectAll(e.target.checked)}
              />
            </th>
          )}
          {columns.map((column) => (
            <th
              key={column.key}
              style={{ width: column.width }}
              className={`${column.className || ''} ${column.align ? `text-${column.align}` : ''} ${column.sortable ? 'cursor-pointer user-select-none' : ''}`}
              onClick={() => handleSort(column)}
            >
              <div className="d-flex align-items-center justify-content-between">
                <span>{column.label}</span>
                {column.sortable && (
                  <span className="ms-2">
                    {internalSortBy === column.key ? (
                      internalSortDirection === 'asc' ? '↑' : '↓'
                    ) : (
                      '↕'
                    )}
                  </span>
                )}
              </div>
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {loading ? (
          <tr>
            <td colSpan={columns.length + (selectable ? 1 : 0)} className="text-center py-4">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </td>
          </tr>
        ) : sortedData.length === 0 ? (
          <tr>
            <td colSpan={columns.length + (selectable ? 1 : 0)} className="text-center py-4 text-muted">
              {emptyMessage}
            </td>
          </tr>
        ) : (
          sortedData.map((row, index) => (
            <tr
              key={index}
              className={`${onRowClick ? 'cursor-pointer' : ''} ${selectedRows.includes(row) ? 'table-active' : ''}`}
              onClick={() => onRowClick?.(row, index)}
            >
              {selectable && (
                <td>
                  <input
                    type="checkbox"
                    className="form-check-input"
                    checked={selectedRows.includes(row)}
                    onChange={(e) => {
                      e.stopPropagation();
                      handleSelectRow(row, e.target.checked);
                    }}
                  />
                </td>
              )}
              {columns.map((column) => (
                <td
                  key={column.key}
                  className={`${column.className || ''} ${column.align ? `text-${column.align}` : ''}`}
                >
                  {column.render ? column.render(row[column.key], row, index) : row[column.key]}
                </td>
              ))}
            </tr>
          ))
        )}
      </tbody>
    </table>
  );

  if (responsive) {
    const responsiveClass = responsive === true ? 'table-responsive' : `table-responsive-${responsive}`;
    return (
      <div className={responsiveClass}>
        {tableElement}
      </div>
    );
  }

  return tableElement;
}
