import React from 'react';

interface EmptyStateProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  action?: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'muted' | 'primary';
}

export default function EmptyState({
  title,
  description,
  icon,
  action,
  className = '',
  size = 'md',
  variant = 'default'
}: EmptyStateProps) {
  const containerClasses = [
    'text-center py-5',
    size === 'sm' ? 'py-3' : '',
    size === 'lg' ? 'py-6' : '',
    variant === 'muted' ? 'text-muted' : '',
    variant === 'primary' ? 'text-primary' : '',
    className
  ].filter(Boolean).join(' ');

  const iconSize = size === 'sm' ? '48px' : size === 'lg' ? '96px' : '64px';
  const titleSize = size === 'sm' ? 'h6' : size === 'lg' ? 'h3' : 'h5';

  return (
    <div className={containerClasses}>
      {icon && (
        <div className="mb-3" style={{ fontSize: iconSize }}>
          {icon}
        </div>
      )}

      <div className={titleSize}>
        {title}
      </div>

      {description && (
        <p className={`mb-4 ${size === 'sm' ? 'small' : ''} ${variant === 'muted' ? 'text-muted' : ''}`}>
          {description}
        </p>
      )}

      {action && (
        <div>
          {action}
        </div>
      )}
    </div>
  );
}

// Predefined Empty States
export function NoDataEmptyState({ action, className }: { action?: React.ReactNode; className?: string }) {
  return (
    <EmptyState
      title="No Data Available"
      description="There's no data to display at the moment."
      icon={
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <circle cx="9" cy="9" r="2"/>
          <path d="M21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
        </svg>
      }
      action={action}
      className={className}
      variant="muted"
    />
  );
}

export function NoResultsEmptyState({ action, className }: { action?: React.ReactNode; className?: string }) {
  return (
    <EmptyState
      title="No Results Found"
      description="Try adjusting your search criteria or filters."
      icon={
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1">
          <circle cx="11" cy="11" r="8"/>
          <path d="M21 21l-4.35-4.35"/>
          <line x1="11" y1="8" x2="11" y2="14"/>
          <line x1="8" y1="11" x2="14" y2="11"/>
        </svg>
      }
      action={action}
      className={className}
      variant="muted"
    />
  );
}

export function ErrorEmptyState({ action, className }: { action?: React.ReactNode; className?: string }) {
  return (
    <EmptyState
      title="Something Went Wrong"
      description="We encountered an error while loading the data. Please try again."
      icon={
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1">
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="8" x2="12" y2="12"/>
          <line x1="12" y1="16" x2="12.01" y2="16"/>
        </svg>
      }
      action={action}
      className={className}
    />
  );
}
