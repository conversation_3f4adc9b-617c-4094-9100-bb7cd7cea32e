import React from 'react';

interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

interface SelectFieldProps {
  label?: string;
  options: SelectOption[];
  value?: string | number;
  onChange?: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLSelectElement>) => void;
  error?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  size?: 'sm' | 'lg';
  className?: string;
  id?: string;
  name?: string;
  placeholder?: string;
  multiple?: boolean;
  autoFocus?: boolean;
}

export default function SelectField({
  label,
  options,
  value,
  onChange,
  onBlur,
  error,
  helperText,
  required = false,
  disabled = false,
  size,
  className = '',
  id,
  name,
  placeholder,
  multiple = false,
  autoFocus = false
}: SelectFieldProps) {
  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;
  const hasError = !!error;

  const sizeClass = size ? `form-select-${size}` : '';
  const errorClass = hasError ? 'is-invalid' : '';

  const selectClasses = [
    'form-select',
    sizeClass,
    errorClass,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className="mb-3">
      {label && (
        <label htmlFor={selectId} className="form-label">
          {label}
          {required && <span className="text-danger ms-1">*</span>}
        </label>
      )}

      <select
        id={selectId}
        name={name}
        className={selectClasses}
        value={value || ''}
        onChange={onChange}
        onBlur={onBlur}
        required={required}
        disabled={disabled}
        multiple={multiple}
        autoFocus={autoFocus}
        aria-describedby={error ? `${selectId}-error` : helperText ? `${selectId}-help` : undefined}
        aria-invalid={hasError}
      >
        {placeholder && !multiple && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))}
      </select>

      {error && (
        <div id={`${selectId}-error`} className="invalid-feedback d-block">
          {error}
        </div>
      )}

      {helperText && !error && (
        <div id={`${selectId}-help`} className="form-text">
          {helperText}
        </div>
      )}
    </div>
  );
}
