import React from 'react';

interface LoaderProps {
  size?: 'sm' | 'lg';
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  type?: 'border' | 'grow';
  message?: string;
  className?: string;
  overlay?: boolean;
  fullScreen?: boolean;
}

export default function Loader({
  size,
  variant = 'primary',
  type = 'border',
  message,
  className = '',
  overlay = false,
  fullScreen = false
}: LoaderProps) {
  const spinnerClasses = [
    type === 'grow' ? 'spinner-grow' : 'spinner-border',
    variant ? `text-${variant}` : '',
    size ? `spinner-${type}-${size}` : '',
    className
  ].filter(Boolean).join(' ');

  const spinner = (
    <div className={spinnerClasses} role="status">
      <span className="visually-hidden">Loading...</span>
    </div>
  );

  const content = (
    <div className="d-flex flex-column align-items-center justify-content-center">
      {spinner}
      {message && (
        <div className="mt-3 text-center">
          <p className="mb-0">{message}</p>
        </div>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div
        className="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-white"
        style={{ zIndex: 9999 }}
      >
        {content}
      </div>
    );
  }

  if (overlay) {
    return (
      <div
        className="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-white bg-opacity-75"
        style={{ zIndex: 1000 }}
      >
        {content}
      </div>
    );
  }

  return content;
}

// Inline Spinner Component
interface SpinnerProps {
  size?: 'sm' | 'lg';
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  type?: 'border' | 'grow';
  className?: string;
}

export function Spinner({
  size,
  variant = 'primary',
  type = 'border',
  className = ''
}: SpinnerProps) {
  const spinnerClasses = [
    type === 'grow' ? 'spinner-grow' : 'spinner-border',
    variant ? `text-${variant}` : '',
    size ? `spinner-${type}-${size}` : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={spinnerClasses} role="status">
      <span className="visually-hidden">Loading...</span>
    </div>
  );
}

// Button Spinner Component
interface ButtonSpinnerProps {
  size?: 'sm';
  className?: string;
}

export function ButtonSpinner({ size, className = '' }: ButtonSpinnerProps) {
  const spinnerClasses = [
    'spinner-border',
    size ? `spinner-border-${size}` : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <span className={spinnerClasses} role="status" aria-hidden="true">
      <span className="visually-hidden">Loading...</span>
    </span>
  );
}
