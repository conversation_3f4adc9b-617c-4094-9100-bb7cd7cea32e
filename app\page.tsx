import Link from "next/link";

export default function Home() {
  return (
    <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto' }}>
      <header style={{ textAlign: 'center', marginBottom: '3rem' }}>
        <h1>KYC Portal</h1>
        <p>Know Your Customer - Compliance Management System</p>
      </header>

      <main>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '2rem' }}>

          {/* Customer Portal */}
          <div style={{
            border: '1px solid #ddd',
            borderRadius: '8px',
            padding: '2rem',
            textAlign: 'center',
            backgroundColor: '#f8f9fa'
          }}>
            <h2>Customer Portal</h2>
            <p>Submit and manage your KYC applications</p>
            <div style={{ marginTop: '1.5rem' }}>
              <Link
                href="/portal/dashboard"
                style={{
                  display: 'inline-block',
                  padding: '0.75rem 1.5rem',
                  backgroundColor: '#007bff',
                  color: 'white',
                  textDecoration: 'none',
                  borderRadius: '4px',
                  marginBottom: '0.5rem'
                }}
              >
                Go to Customer Portal
              </Link>
            </div>
            <div style={{ fontSize: '0.875rem', color: '#666' }}>
              <Link href="/portal/kyc-form" style={{ color: '#007bff', textDecoration: 'none' }}>KYC Form</Link> |
              <Link href="/portal/documents" style={{ color: '#007bff', textDecoration: 'none' }}> Documents</Link> |
              <Link href="/portal/status" style={{ color: '#007bff', textDecoration: 'none' }}> Status</Link>
            </div>
          </div>

          {/* Admin Portal */}
          <div style={{
            border: '1px solid #ddd',
            borderRadius: '8px',
            padding: '2rem',
            textAlign: 'center',
            backgroundColor: '#f8f9fa'
          }}>
            <h2>Admin Portal</h2>
            <p>Manage KYC applications and compliance</p>
            <div style={{ marginTop: '1.5rem' }}>
              <Link
                href="/admin/dashboard"
                style={{
                  display: 'inline-block',
                  padding: '0.75rem 1.5rem',
                  backgroundColor: '#28a745',
                  color: 'white',
                  textDecoration: 'none',
                  borderRadius: '4px',
                  marginBottom: '0.5rem'
                }}
              >
                Go to Admin Portal
              </Link>
            </div>
            <div style={{ fontSize: '0.875rem', color: '#666' }}>
              <Link href="/admin/applications" style={{ color: '#28a745', textDecoration: 'none' }}>Applications</Link> |
              <Link href="/admin/users" style={{ color: '#28a745', textDecoration: 'none' }}> Users</Link> |
              <Link href="/admin/reports" style={{ color: '#28a745', textDecoration: 'none' }}> Reports</Link>
            </div>
          </div>

        </div>

        {/* Authentication Links */}
        <div style={{ textAlign: 'center', marginTop: '3rem', padding: '2rem', backgroundColor: '#e9ecef', borderRadius: '8px' }}>
          <h3>Authentication</h3>
          <div style={{ display: 'flex', justifyContent: 'center', gap: '1rem', flexWrap: 'wrap' }}>
            <Link href="/login" style={{ color: '#007bff', textDecoration: 'none', padding: '0.5rem 1rem', border: '1px solid #007bff', borderRadius: '4px' }}>
              Login
            </Link>
            <Link href="/register" style={{ color: '#007bff', textDecoration: 'none', padding: '0.5rem 1rem', border: '1px solid #007bff', borderRadius: '4px' }}>
              Register
            </Link>
            <Link href="/forgot-password" style={{ color: '#6c757d', textDecoration: 'none', padding: '0.5rem 1rem', border: '1px solid #6c757d', borderRadius: '4px' }}>
              Forgot Password
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
}
