import React from 'react';
import Link from 'next/link';

interface FooterLink {
  label: string;
  href: string;
}

interface FooterSection {
  title: string;
  links: FooterLink[];
}

interface FooterProps {
  sections?: FooterSection[];
  copyright?: string;
  variant?: 'light' | 'dark';
  className?: string;
  socialLinks?: {
    platform: string;
    href: string;
    icon?: React.ReactNode;
  }[];
  companyInfo?: {
    name: string;
    description?: string;
    logo?: string;
  };
}

export default function Footer({
  sections = [],
  copyright = `© ${new Date().getFullYear()} KYC Portal. All rights reserved.`,
  variant = 'light',
  className = '',
  socialLinks = [],
  companyInfo
}: FooterProps) {
  const footerClasses = [
    'footer mt-auto py-4',
    variant === 'dark' ? 'bg-dark text-white' : 'bg-light border-top',
    className
  ].filter(Boolean).join(' ');

  return (
    <footer className={footerClasses}>
      <div className="container">
        {/* Main footer content */}
        {(sections.length > 0 || companyInfo || socialLinks.length > 0) && (
          <div className="row">
            {/* Company Info */}
            {companyInfo && (
              <div className="col-lg-4 mb-4">
                <div className="d-flex align-items-center mb-3">
                  {companyInfo.logo && (
                    <img
                      src={companyInfo.logo}
                      alt={companyInfo.name}
                      height="40"
                      className="me-3"
                    />
                  )}
                  <h5 className="mb-0">{companyInfo.name}</h5>
                </div>
                {companyInfo.description && (
                  <p className="text-muted">{companyInfo.description}</p>
                )}

                {/* Social Links */}
                {socialLinks.length > 0 && (
                  <div className="d-flex gap-3 mt-3">
                    {socialLinks.map((social, index) => (
                      <Link
                        key={index}
                        href={social.href}
                        className={`text-decoration-none ${variant === 'dark' ? 'text-light' : 'text-dark'}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        title={social.platform}
                      >
                        {social.icon || social.platform}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Footer Sections */}
            {sections.map((section, index) => (
              <div key={index} className="col-lg-2 col-md-4 col-sm-6 mb-4">
                <h6 className="fw-bold mb-3">{section.title}</h6>
                <ul className="list-unstyled">
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex} className="mb-2">
                      <Link
                        href={link.href}
                        className={`text-decoration-none ${variant === 'dark' ? 'text-light' : 'text-muted'} hover-text-primary`}
                      >
                        {link.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        )}

        {/* Copyright */}
        {(sections.length > 0 || companyInfo || socialLinks.length > 0) && (
          <hr className={variant === 'dark' ? 'border-secondary' : ''} />
        )}

        <div className="row align-items-center">
          <div className="col-md-6">
            <p className={`mb-0 ${variant === 'dark' ? 'text-light' : 'text-muted'}`}>
              {copyright}
            </p>
          </div>
          <div className="col-md-6 text-md-end">
            <div className="d-flex justify-content-md-end gap-3">
              <Link href="/privacy" className={`text-decoration-none ${variant === 'dark' ? 'text-light' : 'text-muted'}`}>
                Privacy Policy
              </Link>
              <Link href="/terms" className={`text-decoration-none ${variant === 'dark' ? 'text-light' : 'text-muted'}`}>
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

// Simple Footer Component
interface SimpleFooterProps {
  children?: React.ReactNode;
  className?: string;
  variant?: 'light' | 'dark';
}

export function SimpleFooter({ children, className = '', variant = 'light' }: SimpleFooterProps) {
  const footerClasses = [
    'footer mt-auto py-3 text-center',
    variant === 'dark' ? 'bg-dark text-white' : 'bg-light border-top',
    className
  ].filter(Boolean).join(' ');

  return (
    <footer className={footerClasses}>
      <div className="container">
        {children || (
          <p className={`mb-0 ${variant === 'dark' ? 'text-light' : 'text-muted'}`}>
            © {new Date().getFullYear()} KYC Portal. All rights reserved.
          </p>
        )}
      </div>
    </footer>
  );
}
