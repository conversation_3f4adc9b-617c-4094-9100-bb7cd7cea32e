export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: UserRole;
  status: UserStatus;
  createdAt: string;
  updatedAt?: string;
  lastLoginAt?: string;
  kycStatus?: KycStatus;
  profilePicture?: string;
}

export type UserRole = 'customer' | 'admin' | 'compliance';

export type UserStatus = 'active' | 'inactive' | 'suspended' | 'pending';

export type KycStatus = 'not_started' | 'pending' | 'in_review' | 'approved' | 'rejected';

export interface UserProfile {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  nationality?: string;
  address?: Address;
  occupation?: string;
  employerName?: string;
  annualIncome?: number;
  sourceOfFunds?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  phone?: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}
