import React, { useEffect } from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'lg' | 'xl';
  centered?: boolean;
  backdrop?: boolean | 'static';
  keyboard?: boolean;
  className?: string;
  footer?: React.ReactNode;
}

interface ModalHeaderProps {
  children: React.ReactNode;
  onClose?: () => void;
  className?: string;
}

interface ModalBodyProps {
  children: React.ReactNode;
  className?: string;
}

interface ModalFooterProps {
  children: React.ReactNode;
  className?: string;
}

export default function Modal({
  isOpen,
  onClose,
  title,
  children,
  size,
  centered = false,
  backdrop = true,
  keyboard = true,
  className = '',
  footer
}: ModalProps) {
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (keyboard && e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, keyboard, onClose]);

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (backdrop === true && e.target === e.currentTarget) {
      onClose();
    }
  };

  const sizeClass = size ? `modal-${size}` : '';
  const centeredClass = centered ? 'modal-dialog-centered' : '';
  
  const dialogClasses = [
    'modal-dialog',
    sizeClass,
    centeredClass
  ].filter(Boolean).join(' ');

  return (
    <div 
      className={`modal fade show d-block ${className}`}
      style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
    >
      <div className={dialogClasses}>
        <div className="modal-content">
          {title && (
            <ModalHeader onClose={onClose}>
              {title}
            </ModalHeader>
          )}
          <ModalBody>
            {children}
          </ModalBody>
          {footer && (
            <ModalFooter>
              {footer}
            </ModalFooter>
          )}
        </div>
      </div>
    </div>
  );
}

export function ModalHeader({ children, onClose, className = '' }: ModalHeaderProps) {
  return (
    <div className={`modal-header ${className}`}>
      <h5 className="modal-title">{children}</h5>
      {onClose && (
        <button
          type="button"
          className="btn-close"
          aria-label="Close"
          onClick={onClose}
        ></button>
      )}
    </div>
  );
}

export function ModalBody({ children, className = '' }: ModalBodyProps) {
  return (
    <div className={`modal-body ${className}`}>
      {children}
    </div>
  );
}

export function ModalFooter({ children, className = '' }: ModalFooterProps) {
  return (
    <div className={`modal-footer ${className}`}>
      {children}
    </div>
  );
}
