import React from 'react';

interface InputFieldProps {
  label?: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search' | 'date' | 'time' | 'datetime-local';
  placeholder?: string;
  value?: string | number;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  error?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
  size?: 'sm' | 'lg';
  className?: string;
  id?: string;
  name?: string;
  autoComplete?: string;
  autoFocus?: boolean;
  min?: number | string;
  max?: number | string;
  step?: number | string;
  pattern?: string;
  maxLength?: number;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

export default function InputField({
  label,
  type = 'text',
  placeholder,
  value,
  onChange,
  onBlur,
  onFocus,
  error,
  helperText,
  required = false,
  disabled = false,
  readOnly = false,
  size,
  className = '',
  id,
  name,
  autoComplete,
  autoFocus = false,
  min,
  max,
  step,
  pattern,
  maxLength,
  startIcon,
  endIcon
}: InputFieldProps) {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
  const hasError = !!error;

  const sizeClass = size ? `form-control-${size}` : '';
  const errorClass = hasError ? 'is-invalid' : '';

  const inputClasses = [
    'form-control',
    sizeClass,
    errorClass,
    className
  ].filter(Boolean).join(' ');

  const inputGroup = startIcon || endIcon;

  const inputElement = (
    <input
      id={inputId}
      name={name}
      type={type}
      className={inputClasses}
      placeholder={placeholder}
      value={value || ''}
      onChange={onChange}
      onBlur={onBlur}
      onFocus={onFocus}
      required={required}
      disabled={disabled}
      readOnly={readOnly}
      autoComplete={autoComplete}
      autoFocus={autoFocus}
      min={min}
      max={max}
      step={step}
      pattern={pattern}
      maxLength={maxLength}
      aria-describedby={error ? `${inputId}-error` : helperText ? `${inputId}-help` : undefined}
      aria-invalid={hasError}
    />
  );

  return (
    <div className="mb-3">
      {label && (
        <label htmlFor={inputId} className="form-label">
          {label}
          {required && <span className="text-danger ms-1">*</span>}
        </label>
      )}

      {inputGroup ? (
        <div className="input-group">
          {startIcon && (
            <span className="input-group-text">
              {startIcon}
            </span>
          )}
          {inputElement}
          {endIcon && (
            <span className="input-group-text">
              {endIcon}
            </span>
          )}
        </div>
      ) : (
        inputElement
      )}

      {error && (
        <div id={`${inputId}-error`} className="invalid-feedback d-block">
          {error}
        </div>
      )}

      {helperText && !error && (
        <div id={`${inputId}-help`} className="form-text">
          {helperText}
        </div>
      )}
    </div>
  );
}
