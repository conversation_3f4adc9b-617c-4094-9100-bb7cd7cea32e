export const KYC_STATUS = {
  PENDING: 'pending',
  IN_REVIEW: 'in-review',
  APPROVED: 'approved',
  REJECTED: 'rejected',
} as const;

export const USER_ROLES = {
  CUSTOMER: 'customer',
  ADMIN: 'admin',
  COMPLIANCE: 'compliance',
} as const;

export const DOCUMENT_TYPES = {
  PASSPORT: 'passport',
  DRIVERS_LICENSE: 'drivers_license',
  NATIONAL_ID: 'national_id',
  UTILITY_BILL: 'utility_bill',
  BANK_STATEMENT: 'bank_statement',
} as const;

export const FORM_STEPS = [
  'Personal Information',
  'Address Information',
  'Document Upload',
  'Review & Submit',
];

export const API_ENDPOINTS = {
  KYC_APPLICATIONS: '/api/kyc/list',
  USER_PROFILE: '/api/users/profile',
  DOCUMENT_UPLOAD: '/api/documents/upload',
} as const;

export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 25, 50, 100],
} as const;
