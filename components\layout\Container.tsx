import React from 'react';

interface ContainerProps {
  children: React.ReactNode;
  fluid?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  className?: string;
}

export default function Container({ 
  children, 
  fluid = false, 
  size,
  className = '' 
}: ContainerProps) {
  let containerClass = 'container';
  
  if (fluid) {
    containerClass = 'container-fluid';
  } else if (size) {
    containerClass = `container-${size}`;
  }
  
  const containerClasses = [containerClass, className].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {children}
    </div>
  );
}

// Row Component
interface RowProps {
  children: React.ReactNode;
  className?: string;
  noGutters?: boolean;
  alignItems?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';
  justifyContent?: 'start' | 'center' | 'end' | 'around' | 'between' | 'evenly';
}

export function Row({ 
  children, 
  className = '',
  noGutters = false,
  alignItems,
  justifyContent
}: RowProps) {
  const gutterClass = noGutters ? 'g-0' : '';
  const alignClass = alignItems ? `align-items-${alignItems}` : '';
  const justifyClass = justifyContent ? `justify-content-${justifyContent}` : '';
  
  const rowClasses = [
    'row',
    gutterClass,
    alignClass,
    justifyClass,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={rowClasses}>
      {children}
    </div>
  );
}

// Column Component
interface ColProps {
  children: React.ReactNode;
  xs?: number | 'auto';
  sm?: number | 'auto';
  md?: number | 'auto';
  lg?: number | 'auto';
  xl?: number | 'auto';
  xxl?: number | 'auto';
  className?: string;
  offset?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    xxl?: number;
  };
  order?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    xxl?: number;
  };
}

export function Col({ 
  children, 
  xs,
  sm,
  md,
  lg,
  xl,
  xxl,
  className = '',
  offset,
  order
}: ColProps) {
  const colClasses = ['col'];
  
  // Add responsive column classes
  if (xs !== undefined) colClasses.push(xs === 'auto' ? 'col-auto' : `col-${xs}`);
  if (sm !== undefined) colClasses.push(sm === 'auto' ? 'col-sm-auto' : `col-sm-${sm}`);
  if (md !== undefined) colClasses.push(md === 'auto' ? 'col-md-auto' : `col-md-${md}`);
  if (lg !== undefined) colClasses.push(lg === 'auto' ? 'col-lg-auto' : `col-lg-${lg}`);
  if (xl !== undefined) colClasses.push(xl === 'auto' ? 'col-xl-auto' : `col-xl-${xl}`);
  if (xxl !== undefined) colClasses.push(xxl === 'auto' ? 'col-xxl-auto' : `col-xxl-${xxl}`);
  
  // Add offset classes
  if (offset) {
    if (offset.xs) colClasses.push(`offset-${offset.xs}`);
    if (offset.sm) colClasses.push(`offset-sm-${offset.sm}`);
    if (offset.md) colClasses.push(`offset-md-${offset.md}`);
    if (offset.lg) colClasses.push(`offset-lg-${offset.lg}`);
    if (offset.xl) colClasses.push(`offset-xl-${offset.xl}`);
    if (offset.xxl) colClasses.push(`offset-xxl-${offset.xxl}`);
  }
  
  // Add order classes
  if (order) {
    if (order.xs) colClasses.push(`order-${order.xs}`);
    if (order.sm) colClasses.push(`order-sm-${order.sm}`);
    if (order.md) colClasses.push(`order-md-${order.md}`);
    if (order.lg) colClasses.push(`order-lg-${order.lg}`);
    if (order.xl) colClasses.push(`order-xl-${order.xl}`);
    if (order.xxl) colClasses.push(`order-xxl-${order.xxl}`);
  }
  
  // If no specific column sizes are provided, use default 'col'
  if (!xs && !sm && !md && !lg && !xl && !xxl) {
    colClasses.length = 1; // Keep only 'col'
  } else {
    colClasses.shift(); // Remove the default 'col' since we have specific sizes
  }
  
  const finalClasses = [...colClasses, className].filter(Boolean).join(' ');

  return (
    <div className={finalClasses}>
      {children}
    </div>
  );
}
