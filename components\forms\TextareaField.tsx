import React from 'react';

interface TextareaFieldProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  error?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
  rows?: number;
  cols?: number;
  className?: string;
  id?: string;
  name?: string;
  autoFocus?: boolean;
  maxLength?: number;
  minLength?: number;
  resize?: 'none' | 'both' | 'horizontal' | 'vertical';
}

export default function TextareaField({ 
  label,
  placeholder,
  value,
  onChange,
  onBlur,
  onFocus,
  error,
  helperText,
  required = false,
  disabled = false,
  readOnly = false,
  rows = 3,
  cols,
  className = '',
  id,
  name,
  autoFocus = false,
  maxLength,
  minLength,
  resize = 'vertical'
}: TextareaFieldProps) {
  const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;
  const hasError = !!error;
  
  const errorClass = hasError ? 'is-invalid' : '';
  
  const textareaClasses = [
    'form-control',
    errorClass,
    className
  ].filter(Boolean).join(' ');

  const resizeStyle = {
    resize: resize
  };

  return (
    <div className="mb-3">
      {label && (
        <label htmlFor={textareaId} className="form-label">
          {label}
          {required && <span className="text-danger ms-1">*</span>}
        </label>
      )}
      
      <textarea
        id={textareaId}
        name={name}
        className={textareaClasses}
        placeholder={placeholder}
        value={value || ''}
        onChange={onChange}
        onBlur={onBlur}
        onFocus={onFocus}
        required={required}
        disabled={disabled}
        readOnly={readOnly}
        rows={rows}
        cols={cols}
        autoFocus={autoFocus}
        maxLength={maxLength}
        minLength={minLength}
        style={resizeStyle}
        aria-describedby={error ? `${textareaId}-error` : helperText ? `${textareaId}-help` : undefined}
        aria-invalid={hasError}
      />
      
      {maxLength && (
        <div className="form-text text-end">
          {(value?.length || 0)}/{maxLength}
        </div>
      )}
      
      {error && (
        <div id={`${textareaId}-error`} className="invalid-feedback d-block">
          {error}
        </div>
      )}
      
      {helperText && !error && (
        <div id={`${textareaId}-help`} className="form-text">
          {helperText}
        </div>
      )}
    </div>
  );
}
