interface DocumentPreviewModalProps {
  isOpen: boolean;
  documentUrl: string;
  documentName: string;
  onClose: () => void;
}

export default function DocumentPreviewModal({ isOpen, documentUrl, documentName, onClose }: DocumentPreviewModalProps) {
  if (!isOpen) return null;

  return (
    <div>
      <div>
        <div>
          <h3>{documentName}</h3>
          <button onClick={onClose}>Close</button>
        </div>
        <div>
          <iframe src={documentUrl} width="100%" height="500px" />
        </div>
      </div>
    </div>
  );
}
