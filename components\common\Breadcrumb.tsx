import React from 'react';
import Link from 'next/link';

interface BreadcrumbItem {
  label: string;
  href?: string;
  active?: boolean;
  icon?: React.ReactNode;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  separator?: React.ReactNode;
  className?: string;
}

export default function Breadcrumb({ 
  items, 
  separator,
  className = '' 
}: BreadcrumbProps) {
  const breadcrumbClasses = [
    'breadcrumb',
    className
  ].filter(Boolean).join(' ');

  return (
    <nav aria-label="breadcrumb">
      <ol className={breadcrumbClasses}>
        {items.map((item, index) => {
          const isLast = index === items.length - 1;
          const isActive = item.active || isLast;

          return (
            <li
              key={index}
              className={`breadcrumb-item ${isActive ? 'active' : ''}`}
              aria-current={isActive ? 'page' : undefined}
            >
              <div className="d-flex align-items-center">
                {item.icon && (
                  <span className="me-2">{item.icon}</span>
                )}
                {isActive || !item.href ? (
                  <span>{item.label}</span>
                ) : (
                  <Link href={item.href} className="text-decoration-none">
                    {item.label}
                  </Link>
                )}
              </div>
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

// Simple Breadcrumb Component
interface SimpleBreadcrumbProps {
  children: React.ReactNode;
  className?: string;
}

export function SimpleBreadcrumb({ children, className = '' }: SimpleBreadcrumbProps) {
  return (
    <nav aria-label="breadcrumb">
      <ol className={`breadcrumb ${className}`}>
        {children}
      </ol>
    </nav>
  );
}

// Breadcrumb Item Component
interface BreadcrumbItemComponentProps {
  children: React.ReactNode;
  href?: string;
  active?: boolean;
  className?: string;
}

export function BreadcrumbItem({ 
  children, 
  href, 
  active = false, 
  className = '' 
}: BreadcrumbItemComponentProps) {
  return (
    <li
      className={`breadcrumb-item ${active ? 'active' : ''} ${className}`}
      aria-current={active ? 'page' : undefined}
    >
      {active || !href ? (
        children
      ) : (
        <Link href={href} className="text-decoration-none">
          {children}
        </Link>
      )}
    </li>
  );
}
