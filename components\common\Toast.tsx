import React, { useState, useEffect } from 'react';

interface ToastProps {
  id?: string;
  title?: string;
  message: string;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  show?: boolean;
  onClose?: () => void;
  autoHide?: boolean;
  delay?: number;
  position?: 'top-start' | 'top-center' | 'top-end' | 'middle-start' | 'middle-center' | 'middle-end' | 'bottom-start' | 'bottom-center' | 'bottom-end';
  className?: string;
  icon?: React.ReactNode;
}

export default function Toast({
  id,
  title,
  message,
  variant = 'info',
  show = true,
  onClose,
  autoHide = true,
  delay = 5000,
  position = 'top-end',
  className = '',
  icon
}: ToastProps) {
  const [isVisible, setIsVisible] = useState(show);

  useEffect(() => {
    setIsVisible(show);
  }, [show]);

  useEffect(() => {
    if (isVisible && autoHide && delay > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [isVisible, autoHide, delay]);

  const handleClose = () => {
    setIsVisible(false);
    onClose?.();
  };

  if (!isVisible) return null;

  const toastClasses = [
    'toast',
    'show',
    className
  ].filter(Boolean).join(' ');

  const headerClasses = [
    'toast-header',
    variant !== 'light' && variant !== 'dark' ? `bg-${variant} text-white` : ''
  ].filter(Boolean).join(' ');

  return (
    <div className={toastClasses} role="alert" aria-live="assertive" aria-atomic="true">
      {title && (
        <div className={headerClasses}>
          {icon && <span className="me-2">{icon}</span>}
          <strong className="me-auto">{title}</strong>
          <button
            type="button"
            className="btn-close"
            aria-label="Close"
            onClick={handleClose}
          ></button>
        </div>
      )}
      <div className="toast-body d-flex align-items-center">
        {!title && icon && <span className="me-2">{icon}</span>}
        <span className="flex-grow-1">{message}</span>
        {!title && (
          <button
            type="button"
            className="btn-close ms-2"
            aria-label="Close"
            onClick={handleClose}
          ></button>
        )}
      </div>
    </div>
  );
}

// Toast Container Component
interface ToastContainerProps {
  children: React.ReactNode;
  position?: 'top-start' | 'top-center' | 'top-end' | 'middle-start' | 'middle-center' | 'middle-end' | 'bottom-start' | 'bottom-center' | 'bottom-end';
  className?: string;
}

export function ToastContainer({
  children,
  position = 'top-end',
  className = ''
}: ToastContainerProps) {
  const getPositionClasses = (pos: string) => {
    const positions = {
      'top-start': 'top-0 start-0',
      'top-center': 'top-0 start-50 translate-middle-x',
      'top-end': 'top-0 end-0',
      'middle-start': 'top-50 start-0 translate-middle-y',
      'middle-center': 'top-50 start-50 translate-middle',
      'middle-end': 'top-50 end-0 translate-middle-y',
      'bottom-start': 'bottom-0 start-0',
      'bottom-center': 'bottom-0 start-50 translate-middle-x',
      'bottom-end': 'bottom-0 end-0'
    };
    return positions[pos as keyof typeof positions] || positions['top-end'];
  };

  const containerClasses = [
    'toast-container',
    'position-fixed',
    'p-3',
    getPositionClasses(position),
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses} style={{ zIndex: 1050 }}>
      {children}
    </div>
  );
}

// Toast Hook for managing multiple toasts
export interface ToastItem extends Omit<ToastProps, 'show' | 'onClose'> {
  id: string;
}

export function useToast() {
  const [toasts, setToasts] = useState<ToastItem[]>([]);

  const addToast = (toast: Omit<ToastItem, 'id'>) => {
    const id = `toast-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    setToasts(prev => [...prev, { ...toast, id }]);
    return id;
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const clearToasts = () => {
    setToasts([]);
  };

  // Convenience methods
  const success = (message: string, options?: Partial<ToastItem>) => {
    return addToast({ ...options, message, variant: 'success' });
  };

  const error = (message: string, options?: Partial<ToastItem>) => {
    return addToast({ ...options, message, variant: 'danger' });
  };

  const warning = (message: string, options?: Partial<ToastItem>) => {
    return addToast({ ...options, message, variant: 'warning' });
  };

  const info = (message: string, options?: Partial<ToastItem>) => {
    return addToast({ ...options, message, variant: 'info' });
  };

  return {
    toasts,
    addToast,
    removeToast,
    clearToasts,
    success,
    error,
    warning,
    info
  };
}

// Toast Provider Component
interface ToastProviderProps {
  children: React.ReactNode;
  position?: ToastContainerProps['position'];
}

export function ToastProvider({ children, position = 'top-end' }: ToastProviderProps) {
  const { toasts, removeToast } = useToast();

  return (
    <>
      {children}
      <ToastContainer position={position}>
        {toasts.map(toast => (
          <Toast
            key={toast.id}
            {...toast}
            show={true}
            onClose={() => removeToast(toast.id)}
          />
        ))}
      </ToastContainer>
    </>
  );
}
