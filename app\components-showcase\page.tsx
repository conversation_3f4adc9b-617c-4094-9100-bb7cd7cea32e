'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardBody,
  Badge,
  <PERSON>ert,
  <PERSON>dal,
  ModalHeader,
  ModalBody,
  ModalFooter,
  InputField,
  SelectField,
  CheckboxField,
  RadioField,
  Progress,
  Tabs,
  Breadcrumb,
  List,
  DataTable,
  Container,
  Row,
  Col,
  Loader,
  EmptyState,
  Toast,
  ToastContainer
} from '@/components';

export default function ComponentsShowcase() {
  const [showModal, setShowModal] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [selectValue, setSelectValue] = useState('');
  const [checkboxValue, setCheckboxValue] = useState(false);
  const [radioValue, setRadioValue] = useState('');

  // Sample data for table
  const tableColumns = [
    { key: 'id', label: 'ID', sortable: true },
    { key: 'name', label: 'Name', sortable: true },
    { key: 'email', label: 'Email', sortable: true },
    { key: 'status', label: 'Status', render: (value: string) => <Badge variant={value === 'active' ? 'success' : 'secondary'}>{value}</Badge> }
  ];

  const tableData = [
    { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive' },
    { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active' }
  ];

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Components', href: '/components' },
    { label: 'Showcase', active: true }
  ];

  const tabItems = [
    { id: 'forms', label: 'Forms', content: <FormsTab /> },
    { id: 'data', label: 'Data Display', content: <DataTab /> },
    { id: 'feedback', label: 'Feedback', content: <FeedbackTab /> }
  ];

  const selectOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' }
  ];

  const radioOptions = [
    { value: 'radio1', label: 'Radio Option 1' },
    { value: 'radio2', label: 'Radio Option 2' },
    { value: 'radio3', label: 'Radio Option 3' }
  ];

  return (
    <Container>
      <div className="py-5">
        <div className="text-center mb-5">
          <h1 className="display-4 fw-bold text-primary">KYC Portal Components</h1>
          <p className="lead text-muted">A comprehensive showcase of all available Bootstrap-based components</p>
        </div>

        <Breadcrumb items={breadcrumbItems} className="mb-4" />

        <Tabs items={tabItems} />

        {/* Modal */}
        <Modal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          title="Sample Modal"
          footer={
            <>
              <Button variant="secondary" onClick={() => setShowModal(false)}>
                Close
              </Button>
              <Button variant="primary" onClick={() => setShowModal(false)}>
                Save Changes
              </Button>
            </>
          }
        >
          <p>This is a sample modal content. You can put any content here.</p>
        </Modal>
      </div>
    </Container>
  );
}

function FormsTab() {
  const [inputValue, setInputValue] = useState('');
  const [selectValue, setSelectValue] = useState('');
  const [checkboxValue, setCheckboxValue] = useState(false);
  const [radioValue, setRadioValue] = useState('');

  const selectOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' }
  ];

  const radioOptions = [
    { value: 'radio1', label: 'Radio Option 1' },
    { value: 'radio2', label: 'Radio Option 2' },
    { value: 'radio3', label: 'Radio Option 3' }
  ];

  return (
    <Row>
      <Col md={6}>
        <Card>
          <CardHeader>
            <h5>Form Components</h5>
          </CardHeader>
          <CardBody>
            <InputField
              label="Text Input"
              placeholder="Enter some text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              helperText="This is a helper text"
            />

            <SelectField
              label="Select Field"
              options={selectOptions}
              value={selectValue}
              onChange={(e) => setSelectValue(e.target.value)}
              placeholder="Choose an option"
            />

            <CheckboxField
              label="Checkbox Field"
              checked={checkboxValue}
              onChange={(e) => setCheckboxValue(e.target.checked)}
            />

            <RadioField
              label="Radio Field"
              options={radioOptions}
              value={radioValue}
              onChange={setRadioValue}
            />
          </CardBody>
        </Card>
      </Col>
      <Col md={6}>
        <Card>
          <CardHeader>
            <h5>Buttons & Badges</h5>
          </CardHeader>
          <CardBody>
            <div className="d-flex gap-2 mb-3">
              <Button variant="primary">Primary</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="success">Success</Button>
              <Button variant="danger">Danger</Button>
            </div>
            <div className="d-flex gap-2 mb-3">
              <Badge variant="primary">Primary</Badge>
              <Badge variant="success">Success</Badge>
              <Badge variant="warning">Warning</Badge>
              <Badge variant="danger">Danger</Badge>
            </div>
            <div className="d-flex gap-2">
              <Button size="sm">Small</Button>
              <Button>Default</Button>
              <Button size="lg">Large</Button>
            </div>
          </CardBody>
        </Card>
      </Col>
    </Row>
  );
}

function DataTab() {
  const tableColumns = [
    { key: 'id', label: 'ID', sortable: true },
    { key: 'name', label: 'Name', sortable: true },
    { key: 'email', label: 'Email', sortable: true },
    { key: 'status', label: 'Status', render: (value: string) => <Badge variant={value === 'active' ? 'success' : 'secondary'}>{value}</Badge> }
  ];

  const tableData = [
    { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive' },
    { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active' }
  ];

  const listItems = [
    { id: 1, content: 'First item', badge: { text: '3', variant: 'primary' as const } },
    { id: 2, content: 'Second item', badge: { text: 'New', variant: 'success' as const } },
    { id: 3, content: 'Third item', active: true }
  ];

  return (
    <Row>
      <Col md={8}>
        <Card>
          <CardHeader>
            <h5>Data Table</h5>
          </CardHeader>
          <CardBody>
            <DataTable
              columns={tableColumns}
              data={tableData}
              striped
              hover
            />
          </CardBody>
        </Card>
      </Col>
      <Col md={4}>
        <Card>
          <CardHeader>
            <h5>List Component</h5>
          </CardHeader>
          <CardBody>
            <List items={listItems} />
          </CardBody>
        </Card>
      </Col>
    </Row>
  );
}

function FeedbackTab() {
  const [showModal, setShowModal] = useState(false);

  return (
    <Row>
      <Col md={6}>
        <Card>
          <CardHeader>
            <h5>Alerts & Progress</h5>
          </CardHeader>
          <CardBody>
            <Alert variant="success" dismissible title="Success!">
              This is a success alert with a title.
            </Alert>
            <Alert variant="warning">
              This is a warning alert without a title.
            </Alert>
            <Progress value={75} label="Upload Progress" showValue />
            <div className="mt-3">
              <Progress value={45} variant="success" striped animated />
            </div>
          </CardBody>
        </Card>
      </Col>
      <Col md={6}>
        <Card>
          <CardHeader>
            <h5>Loading States</h5>
          </CardHeader>
          <CardBody>
            <div className="d-flex gap-3 mb-3">
              <Loader size="sm" />
              <Loader />
              <Loader size="lg" />
            </div>
            <Button onClick={() => setShowModal(true)}>
              Show Modal
            </Button>
            <div className="mt-3">
              <EmptyState
                title="No Data"
                description="There's nothing to show here."
                size="sm"
              />
            </div>
          </CardBody>
        </Card>
      </Col>
    </Row>
  );
}
